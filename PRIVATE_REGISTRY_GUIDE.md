# 私服库部署指南

## 🎯 概述

本指南详细说明如何将 task-master-ai 包发布到私有NPM注册表，包括企业内部的Nexus、Artifactory、GitLab等私服库。

## 📊 当前发布配置分析

### 现有GitHub Workflows

1. **`release.yml`** - 发布到公共NPM
   - 使用 Changesets 管理版本
   - 通过 `NPM_TOKEN` 认证
   - 发布到 `registry.npmjs.org`

2. **`pre-release.yml`** - 预发布版本
   - 支持RC版本发布
   - 手动触发workflow

3. **`ci.yml`** - 持续集成
   - 代码格式检查
   - 单元测试

## 🏢 私服库配置方案

### 方案1：单一私服库发布

#### 1.1 配置私服库信息

```bash
# 交互式配置
npm run registry:configure

# 查看当前配置
npm run registry:show

# 重置为公共NPM
npm run registry:reset
```

#### 1.2 支持的私服库类型

| 类型 | 示例URL | 认证方式 |
|------|---------|----------|
| **Private NPM** | `https://npm.your-company.com/` | Token |
| **Nexus** | `https://nexus.company.com/repository/npm-private/` | Token |
| **Artifactory** | `https://company.jfrog.io/artifactory/api/npm/npm-local/` | Token |
| **GitLab** | `https://gitlab.company.com/api/v4/projects/ID/packages/npm/` | Token |

#### 1.3 GitHub Secrets配置

在GitHub仓库设置中添加以下Secrets：

```bash
# 私有NPM注册表
PRIVATE_NPM_TOKEN=your-private-npm-token

# Nexus
NEXUS_NPM_TOKEN=your-nexus-token

# Artifactory  
ARTIFACTORY_TOKEN=your-artifactory-token

# GitLab
GITLAB_TOKEN=your-gitlab-token
**********************-project-id

# 可选：私有作用域
PRIVATE_SCOPE=your-company
```

### 方案2：多注册表同时发布

#### 2.1 使用多注册表workflow

```bash
# 手动触发多注册表发布
# 在GitHub Actions页面选择 "Multi-Registry Release"
# 输入目标注册表：npm,private,nexus
```

#### 2.2 配置示例

**发布到多个注册表：**
- 公共NPM：`task-master-ai`
- 私有NPM：`@your-company/task-master-ai`
- Nexus：`@nexus/task-master-ai`

## 🔧 具体配置步骤

### 步骤1：配置私服库

```bash
# 1. 运行配置脚本
npm run registry:configure

# 2. 选择目标注册表
# 3. 输入自定义URL（如果需要）
# 4. 输入自定义作用域（如果需要）
# 5. 确认更新package.json和.npmrc
```

### 步骤2：设置认证

#### 本地开发环境

```bash
# 设置环境变量
export PRIVATE_NPM_TOKEN="your-token"

# 或者手动编辑.npmrc
echo "//npm.your-company.com/:_authToken=your-token" >> ~/.npmrc
```

#### CI/CD环境

在GitHub仓库的Settings > Secrets中添加相应的token。

### 步骤3：测试发布

```bash
# 测试包内容
npm pack --dry-run

# 测试发布（不实际发布）
npm publish --dry-run

# 实际发布
npm publish
```

## 🚀 自动化发布流程

### 使用现有workflow

1. **触发私服库发布：**
   ```bash
   # 推送到main分支会触发release-private.yml
   git push origin main
   
   # 或手动触发
   # 在GitHub Actions页面选择 "Release to Private Registry"
   ```

2. **多注册表发布：**
   ```bash
   # 在GitHub Actions页面选择 "Multi-Registry Release"
   # 输入：target_registries: "npm,private"
   # 输入：version_bump: "patch"
   ```

### 自定义workflow

如果需要特定的发布流程，可以修改 `.github/workflows/release-private.yml`：

```yaml
# 修改注册表URL
REGISTRY_URL: "https://your-custom-registry.com/"

# 修改认证token
NODE_AUTH_TOKEN: ${{ secrets.YOUR_CUSTOM_TOKEN }}
```

## 📦 MCP配置更新

### 公共NPM配置

```json
{
  "mcpServers": {
    "task-master": {
      "command": "npx",
      "args": ["-y", "--package=task-master-ai", "task-master-mcp"]
    }
  }
}
```

### 私服库配置

```json
{
  "mcpServers": {
    "task-master": {
      "command": "npx",
      "args": ["-y", "--package=@your-company/task-master-ai", "task-master-mcp"],
      "env": {
        "NPM_CONFIG_REGISTRY": "https://npm.your-company.com/"
      }
    }
  }
}
```

### 用户端配置

用户需要配置npm注册表：

```bash
# 全局配置
npm config set registry https://npm.your-company.com/
npm config set //npm.your-company.com/:_authToken your-token

# 或使用.npmrc文件
echo "registry=https://npm.your-company.com/" > ~/.npmrc
echo "//npm.your-company.com/:_authToken=your-token" >> ~/.npmrc
```

## 🔍 故障排除

### 常见问题

#### 1. 认证失败

**错误：** `401 Unauthorized`

**解决方案：**
```bash
# 检查token是否正确
npm whoami --registry=https://npm.your-company.com/

# 重新设置token
npm config set //npm.your-company.com/:_authToken your-new-token
```

#### 2. 包名冲突

**错误：** `403 Forbidden - Package name already exists`

**解决方案：**
```bash
# 使用作用域
npm run registry:configure
# 选择自定义作用域：your-company
```

#### 3. 网络连接问题

**错误：** `ENOTFOUND` 或连接超时

**解决方案：**
```bash
# 检查网络连接
curl -I https://npm.your-company.com/

# 检查代理设置
npm config get proxy
npm config get https-proxy
```

### 调试命令

```bash
# 查看npm配置
npm config list

# 查看当前注册表
npm config get registry

# 测试连接
npm ping --registry=https://npm.your-company.com/

# 查看包信息
npm view @your-company/task-master-ai --registry=https://npm.your-company.com/
```

## 📊 监控和维护

### 发布监控

1. **GitHub Actions监控**
   - 查看workflow执行状态
   - 检查发布日志

2. **注册表监控**
   - 检查包是否成功发布
   - 验证版本号正确性

3. **用户反馈**
   - 收集安装和使用问题
   - 监控下载统计

### 定期维护

```bash
# 定期更新依赖
npm audit
npm update

# 清理旧版本（如果注册表支持）
npm unpublish @your-company/task-master-ai@old-version

# 备份重要版本
npm pack @your-company/task-master-ai@stable-version
```

## 🎯 最佳实践

1. **版本管理**
   - 使用语义化版本
   - 为不同环境使用不同版本标签

2. **安全性**
   - 定期轮换认证token
   - 使用最小权限原则
   - 启用双因素认证

3. **文档**
   - 维护内部安装文档
   - 提供故障排除指南
   - 记录配置变更

4. **测试**
   - 在发布前进行充分测试
   - 使用staging环境验证
   - 自动化测试流程

## 📞 支持

- **内部支持**：联系DevOps团队
- **技术文档**：查看私服库管理文档
- **GitHub Issues**：报告技术问题

---

通过以上配置，您可以成功将task-master-ai包发布到企业私服库，并为团队提供安全、可控的包管理解决方案。🚀
