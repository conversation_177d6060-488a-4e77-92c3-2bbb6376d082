#!/usr/bin/env node

/**
 * NPM Release Preparation Script
 * Prepares the package for NPM/NPX distribution
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🚀 Preparing NPM release for task-master-ai...\n');

// Check if we're logged into npm
function checkNpmAuth() {
    try {
        const whoami = execSync('npm whoami', { encoding: 'utf8' }).trim();
        console.log(`✅ Logged into NPM as: ${whoami}`);
        return true;
    } catch (error) {
        console.error('❌ Not logged into NPM. Please run: npm login');
        return false;
    }
}

// Run tests
function runTests() {
    console.log('🧪 Running tests...');
    try {
        execSync('npm test', { stdio: 'inherit', cwd: projectRoot });
        console.log('✅ All tests passed\n');
        return true;
    } catch (error) {
        console.error('❌ Tests failed. Please fix before publishing.');
        return false;
    }
}

// Check package.json configuration
function validatePackageJson() {
    console.log('📋 Validating package.json...');
    
    const packagePath = path.join(projectRoot, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const issues = [];
    
    // Check required fields
    if (!packageJson.name) issues.push('Missing "name" field');
    if (!packageJson.version) issues.push('Missing "version" field');
    if (!packageJson.description) issues.push('Missing "description" field');
    if (!packageJson.bin) issues.push('Missing "bin" field');
    if (!packageJson.repository) issues.push('Missing "repository" field');
    if (!packageJson.keywords || packageJson.keywords.length === 0) {
        issues.push('Missing or empty "keywords" field');
    }
    
    // Check bin files exist
    if (packageJson.bin) {
        Object.entries(packageJson.bin).forEach(([name, binPath]) => {
            const fullPath = path.resolve(projectRoot, binPath);
            if (!fs.existsSync(fullPath)) {
                issues.push(`Binary file not found: ${binPath}`);
            } else {
                // Check if file is executable
                try {
                    fs.accessSync(fullPath, fs.constants.X_OK);
                } catch (error) {
                    issues.push(`Binary file not executable: ${binPath}`);
                }
            }
        });
    }
    
    // Check files array
    if (packageJson.files) {
        packageJson.files.forEach(pattern => {
            // Basic check for critical directories
            if (pattern.includes('bin/') || pattern.includes('scripts/') || pattern.includes('src/')) {
                const dirPath = path.resolve(projectRoot, pattern.replace('/**', ''));
                if (!fs.existsSync(dirPath)) {
                    issues.push(`Files pattern references non-existent path: ${pattern}`);
                }
            }
        });
    }
    
    if (issues.length > 0) {
        console.error('❌ Package.json validation failed:');
        issues.forEach(issue => console.error(`  - ${issue}`));
        return false;
    }
    
    console.log('✅ Package.json validation passed');
    console.log(`  - Name: ${packageJson.name}`);
    console.log(`  - Version: ${packageJson.version}`);
    console.log(`  - Description: ${packageJson.description}`);
    console.log(`  - Binary commands: ${Object.keys(packageJson.bin).join(', ')}`);
    console.log('');
    
    return true;
}

// Check if files are properly included
function validateFiles() {
    console.log('📁 Validating included files...');
    
    try {
        // Use npm pack --dry-run to see what would be included
        const output = execSync('npm pack --dry-run', { 
            encoding: 'utf8', 
            cwd: projectRoot 
        });
        
        const lines = output.split('\n');
        const files = lines
            .filter(line => line.trim() && !line.includes('npm notice'))
            .map(line => line.trim());
        
        console.log(`✅ Package will include ${files.length} files`);
        
        // Check for critical files
        const criticalFiles = ['bin/', 'scripts/', 'src/', 'package.json'];
        const missingCritical = criticalFiles.filter(file => 
            !files.some(f => f.includes(file))
        );
        
        if (missingCritical.length > 0) {
            console.error('❌ Missing critical files:');
            missingCritical.forEach(file => console.error(`  - ${file}`));
            return false;
        }
        
        console.log('✅ All critical files included\n');
        return true;
        
    } catch (error) {
        console.error('❌ Failed to validate files:', error.message);
        return false;
    }
}

// Generate usage documentation
function generateUsageDocs() {
    console.log('📖 Generating usage documentation...');
    
    const usageDoc = `# NPX Usage Guide for task-master-ai

## Quick Start

\`\`\`bash
# Run directly with npx (no installation required)
npx task-master-ai --help

# Initialize configuration
npx task-master-ai models --setup

# Parse a PRD document
npx task-master-ai parse-prd your-requirements.md

# Add a new task
npx task-master-ai add-task "Implement user authentication"

# List all tasks
npx task-master-ai list-tasks
\`\`\`

## Global Installation (Optional)

\`\`\`bash
# Install globally for easier access
npm install -g task-master-ai

# Now you can use without npx
task-master-ai --help
\`\`\`

## Available Commands

- \`models\` - Configure AI models and API keys
- \`parse-prd\` - Parse Product Requirements Documents
- \`add-task\` - Add new tasks to the project
- \`list-tasks\` - List all tasks
- \`update-task\` - Update existing tasks
- \`delete-task\` - Delete tasks
- \`export\` - Export tasks to various formats

## Configuration

The tool will create a \`.taskmaster\` directory in your project root for configuration and data storage.

## Environment Variables

\`\`\`bash
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
export TASKMASTER_DEBUG="true"  # Enable debug mode
\`\`\`

## Examples

\`\`\`bash
# Parse PRD with specific number of tasks
npx task-master-ai parse-prd requirements.md --num-tasks 10

# Export tasks to JSON
npx task-master-ai export --format json --output tasks.json

# Add task with priority
npx task-master-ai add-task "Setup CI/CD pipeline" --priority high
\`\`\`

For more information, visit: https://github.com/eyaltoledano/claude-task-master
`;

    fs.writeFileSync(path.join(projectRoot, 'NPX_USAGE.md'), usageDoc);
    console.log('✅ Usage documentation generated: NPX_USAGE.md\n');
}

// Create a test installation
function testInstallation() {
    console.log('🧪 Testing package installation...');
    
    try {
        // Create a temporary directory for testing
        const testDir = path.join(projectRoot, 'temp-test-install');
        if (fs.existsSync(testDir)) {
            fs.rmSync(testDir, { recursive: true });
        }
        fs.mkdirSync(testDir);
        
        // Pack the package
        console.log('  📦 Creating package tarball...');
        const packOutput = execSync('npm pack', { 
            encoding: 'utf8', 
            cwd: projectRoot 
        }).trim();
        
        const tarballName = packOutput.split('\n').pop();
        const tarballPath = path.join(projectRoot, tarballName);
        
        // Install the package in test directory
        console.log('  📥 Installing package locally...');
        execSync(`npm install ${tarballPath}`, {
            cwd: testDir,
            stdio: 'pipe'
        });
        
        // Test the binary
        console.log('  🧪 Testing binary execution...');
        const testOutput = execSync('npx task-master-ai --version', {
            cwd: testDir,
            encoding: 'utf8'
        });
        
        console.log(`✅ Installation test passed - Version: ${testOutput.trim()}`);
        
        // Cleanup
        fs.rmSync(testDir, { recursive: true });
        fs.unlinkSync(tarballPath);
        
        console.log('✅ Test installation completed successfully\n');
        return true;
        
    } catch (error) {
        console.error('❌ Installation test failed:', error.message);
        return false;
    }
}

// Main preparation process
async function main() {
    console.log('🎯 NPM Release Preparation Checklist\n');
    
    const checks = [
        { name: 'NPM Authentication', fn: checkNpmAuth },
        { name: 'Package.json Validation', fn: validatePackageJson },
        { name: 'Files Validation', fn: validateFiles },
        { name: 'Test Suite', fn: runTests },
        { name: 'Test Installation', fn: testInstallation }
    ];
    
    let allPassed = true;
    
    for (const check of checks) {
        console.log(`🔍 ${check.name}...`);
        const passed = check.fn();
        if (!passed) {
            allPassed = false;
            break;
        }
    }
    
    if (allPassed) {
        generateUsageDocs();
        
        console.log('🎉 All checks passed! Ready for NPM release.');
        console.log('\n📋 Next steps:');
        console.log('1. Review the generated NPX_USAGE.md file');
        console.log('2. Run: npm run changeset (if using changesets)');
        console.log('3. Run: npm run version (to update version)');
        console.log('4. Run: npm run release (to publish)');
        console.log('\n🚀 Or publish directly:');
        console.log('   npm publish');
        console.log('\n✨ After publishing, users can run:');
        console.log('   npx task-master-ai --help');
        
    } else {
        console.log('\n❌ Some checks failed. Please fix the issues above before publishing.');
        process.exit(1);
    }
}

main().catch(error => {
    console.error('❌ Preparation failed:', error);
    process.exit(1);
});
