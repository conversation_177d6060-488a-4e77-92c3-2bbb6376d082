{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Run Dev Script",
      "runtimeExecutable": "node",
      "program": "${workspaceFolder}/scripts/dev.js",
      "cwd": "${workspaceFolder}",
      "args": [],
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Run MCP Server",
      "runtimeExecutable": "node",
      "program": "${workspaceFolder}/mcp-server/server.js",
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Run Tests",
      "runtimeExecutable": "node",
      "runtimeArgs": ["--experimental-vm-modules"],
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal"
    }
  ]
}
