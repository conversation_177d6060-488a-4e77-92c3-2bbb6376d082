#!/usr/bin/env node

/**
 * Private Registry Configuration Script
 * Configures package.json and npm for private registry publishing
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import inquirer from 'inquirer';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Registry configurations
const REGISTRY_CONFIGS = {
    npm: {
        name: 'NPM Public Registry',
        url: 'https://registry.npmjs.org/',
        authToken: 'NPM_TOKEN',
        scope: null,
        access: 'public'
    },
    private: {
        name: 'Private NPM Registry',
        url: 'https://npm.your-company.com/',
        authToken: 'PRIVATE_NPM_TOKEN',
        scope: 'your-company',
        access: 'restricted'
    },
    nexus: {
        name: 'Nexus Repository',
        url: 'https://nexus.your-company.com/repository/npm-private/',
        authToken: 'NEXUS_NPM_TOKEN',
        scope: 'nexus',
        access: 'restricted'
    },
    artifactory: {
        name: 'J<PERSON>rog Artifactory',
        url: 'https://your-company.jfrog.io/artifactory/api/npm/npm-local/',
        authToken: 'ARTIFACTORY_TOKEN',
        scope: 'jfrog',
        access: 'restricted'
    },
    gitlab: {
        name: 'GitLab Package Registry',
        url: 'https://gitlab.your-company.com/api/v4/projects/PROJECT_ID/packages/npm/',
        authToken: 'GITLAB_TOKEN',
        scope: 'gitlab',
        access: 'restricted'
    }
};

// Read package.json
function readPackageJson() {
    const packagePath = path.join(projectRoot, 'package.json');
    return JSON.parse(fs.readFileSync(packagePath, 'utf8'));
}

// Write package.json
function writePackageJson(packageData) {
    const packagePath = path.join(projectRoot, 'package.json');
    fs.writeFileSync(packagePath, JSON.stringify(packageData, null, 2) + '\n');
}

// Configure package for registry
function configurePackageForRegistry(packageData, registryKey, customConfig = {}) {
    const config = { ...REGISTRY_CONFIGS[registryKey], ...customConfig };
    const newPackage = { ...packageData };
    
    // Update package name with scope
    if (config.scope && !newPackage.name.startsWith('@')) {
        newPackage.name = `@${config.scope}/${newPackage.name}`;
    }
    
    // Add publishConfig
    newPackage.publishConfig = {
        registry: config.url,
        access: config.access
    };
    
    // Add repository info for private registries
    if (registryKey !== 'npm') {
        newPackage.private = false; // Ensure it's publishable
    }
    
    return newPackage;
}

// Generate .npmrc content
function generateNpmrc(registryKey, customConfig = {}) {
    const config = { ...REGISTRY_CONFIGS[registryKey], ...customConfig };
    
    let npmrcContent = `registry=${config.url}\n`;
    
    // Add auth token placeholder
    const authLine = config.url.replace('https:', '').replace('http:', '');
    npmrcContent += `${authLine}:_authToken=\${${config.authToken}}\n`;
    
    return npmrcContent;
}

// Interactive configuration
async function interactiveConfig() {
    console.log(chalk.blue.bold('\n🔧 Private Registry Configuration\n'));
    
    const answers = await inquirer.prompt([
        {
            type: 'list',
            name: 'registry',
            message: 'Select target registry:',
            choices: Object.entries(REGISTRY_CONFIGS).map(([key, config]) => ({
                name: `${config.name} (${config.url})`,
                value: key
            }))
        },
        {
            type: 'input',
            name: 'customUrl',
            message: 'Custom registry URL (leave empty to use default):',
            when: (answers) => answers.registry !== 'npm'
        },
        {
            type: 'input',
            name: 'customScope',
            message: 'Custom scope (leave empty to use default):',
            when: (answers) => answers.registry !== 'npm'
        },
        {
            type: 'confirm',
            name: 'updatePackageJson',
            message: 'Update package.json with registry configuration?',
            default: true
        },
        {
            type: 'confirm',
            name: 'generateNpmrc',
            message: 'Generate .npmrc file?',
            default: true
        }
    ]);
    
    return answers;
}

// Main configuration function
async function configureRegistry() {
    try {
        const packageData = readPackageJson();
        const answers = await interactiveConfig();
        
        // Prepare custom config
        const customConfig = {};
        if (answers.customUrl) customConfig.url = answers.customUrl;
        if (answers.customScope) customConfig.scope = answers.customScope;
        
        console.log(chalk.cyan('\n📝 Configuring registry...\n'));
        
        // Update package.json
        if (answers.updatePackageJson) {
            const updatedPackage = configurePackageForRegistry(
                packageData, 
                answers.registry, 
                customConfig
            );
            
            writePackageJson(updatedPackage);
            console.log(chalk.green('✅ Updated package.json'));
            console.log(chalk.gray(`   Name: ${updatedPackage.name}`));
            console.log(chalk.gray(`   Registry: ${updatedPackage.publishConfig.registry}`));
            console.log(chalk.gray(`   Access: ${updatedPackage.publishConfig.access}`));
        }
        
        // Generate .npmrc
        if (answers.generateNpmrc) {
            const npmrcContent = generateNpmrc(answers.registry, customConfig);
            const npmrcPath = path.join(projectRoot, '.npmrc');
            
            fs.writeFileSync(npmrcPath, npmrcContent);
            console.log(chalk.green('✅ Generated .npmrc file'));
            console.log(chalk.gray(`   Path: ${npmrcPath}`));
        }
        
        // Show next steps
        console.log(chalk.blue.bold('\n📋 Next Steps:\n'));
        
        if (answers.registry !== 'npm') {
            const config = { ...REGISTRY_CONFIGS[answers.registry], ...customConfig };
            
            console.log('1. Set up authentication:');
            console.log(chalk.gray(`   export ${config.authToken}="your-token-here"`));
            console.log(chalk.gray(`   # Or add to GitHub Secrets for CI/CD`));
            
            console.log('\n2. Test publishing:');
            console.log(chalk.gray('   npm publish --dry-run'));
            
            console.log('\n3. Publish to registry:');
            console.log(chalk.gray('   npm publish'));
            
            console.log('\n4. Update MCP configuration:');
            console.log(chalk.gray('   {'));
            console.log(chalk.gray('     "mcpServers": {'));
            console.log(chalk.gray('       "task-master": {'));
            console.log(chalk.gray('         "command": "npx",'));
            console.log(chalk.gray(`         "args": ["-y", "--package=${updatedPackage?.name || packageData.name}", "task-master-mcp"]`));
            console.log(chalk.gray('       }'));
            console.log(chalk.gray('     }'));
            console.log(chalk.gray('   }'));
        }
        
        console.log('\n5. Configure CI/CD:');
        console.log(chalk.gray('   - Add registry tokens to GitHub Secrets'));
        console.log(chalk.gray('   - Update workflow files if needed'));
        console.log(chalk.gray('   - Test automated publishing'));
        
    } catch (error) {
        console.error(chalk.red(`❌ Configuration failed: ${error.message}`));
        process.exit(1);
    }
}

// Show current configuration
function showCurrentConfig() {
    try {
        const packageData = readPackageJson();
        
        console.log(chalk.blue.bold('\n📋 Current Configuration\n'));
        console.log(chalk.cyan('Package Information:'));
        console.log(`  Name: ${packageData.name}`);
        console.log(`  Version: ${packageData.version}`);
        
        if (packageData.publishConfig) {
            console.log(chalk.cyan('\nPublish Configuration:'));
            console.log(`  Registry: ${packageData.publishConfig.registry}`);
            console.log(`  Access: ${packageData.publishConfig.access}`);
        } else {
            console.log(chalk.yellow('\n⚠️  No publish configuration found'));
        }
        
        // Check .npmrc
        const npmrcPath = path.join(projectRoot, '.npmrc');
        if (fs.existsSync(npmrcPath)) {
            console.log(chalk.cyan('\n.npmrc Configuration:'));
            const npmrcContent = fs.readFileSync(npmrcPath, 'utf8');
            console.log(chalk.gray(npmrcContent));
        } else {
            console.log(chalk.yellow('\n⚠️  No .npmrc file found'));
        }
        
    } catch (error) {
        console.error(chalk.red(`❌ Error reading configuration: ${error.message}`));
    }
}

// Reset to NPM public registry
function resetToNpm() {
    try {
        const packageData = readPackageJson();
        
        // Remove scope from name
        if (packageData.name.startsWith('@')) {
            packageData.name = packageData.name.split('/')[1];
        }
        
        // Remove publishConfig
        delete packageData.publishConfig;
        
        // Ensure public access
        packageData.private = false;
        
        writePackageJson(packageData);
        
        // Remove .npmrc
        const npmrcPath = path.join(projectRoot, '.npmrc');
        if (fs.existsSync(npmrcPath)) {
            fs.unlinkSync(npmrcPath);
        }
        
        console.log(chalk.green('✅ Reset to NPM public registry'));
        console.log(chalk.gray(`   Name: ${packageData.name}`));
        
    } catch (error) {
        console.error(chalk.red(`❌ Reset failed: ${error.message}`));
    }
}

// CLI interface
async function main() {
    const command = process.argv[2];
    
    switch (command) {
        case 'configure':
        case undefined:
            await configureRegistry();
            break;
        case 'show':
        case 'status':
            showCurrentConfig();
            break;
        case 'reset':
            resetToNpm();
            break;
        case 'help':
        case '--help':
        case '-h':
            console.log(chalk.blue.bold('🔧 Private Registry Configuration Tool\n'));
            console.log('Usage: node scripts/configure-private-registry.js [command]\n');
            console.log('Commands:');
            console.log('  configure  Configure private registry (default)');
            console.log('  show       Show current configuration');
            console.log('  reset      Reset to NPM public registry');
            console.log('  help       Show this help message');
            break;
        default:
            console.log(chalk.red(`Unknown command: ${command}`));
            console.log('Run with --help for usage information');
            process.exit(1);
    }
}

// Run main function
main().catch(error => {
    console.error(chalk.red(`❌ Script failed: ${error.message}`));
    process.exit(1);
});
