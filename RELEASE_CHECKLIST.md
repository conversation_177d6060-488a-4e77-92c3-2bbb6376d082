# NPM/NPX Release Checklist for task-master-ai

## 📋 Pre-Release Checklist

### ✅ Code Quality
- [ ] All tests pass (`npm test`)
- [ ] Code is formatted (`npm run format`)
- [ ] No linting errors
- [ ] Documentation is up to date

### ✅ Package Configuration
- [ ] `package.json` has correct name: `task-master-ai`
- [ ] Version is updated appropriately
- [ ] `bin` field points to correct executable files
- [ ] `files` field includes all necessary files
- [ ] `keywords` are relevant and complete
- [ ] `repository` URL is correct
- [ ] `homepage` and `bugs` URLs are set

### ✅ Binary Files
- [ ] `bin/task-master.js` has shebang (`#!/usr/bin/env node`)
- [ ] Binary files are executable (`chmod +x`)
- [ ] Binary files work when run directly

### ✅ Dependencies
- [ ] All dependencies are in `dependencies` (not `devDependencies`)
- [ ] No unnecessary dependencies
- [ ] Version ranges are appropriate
- [ ] Security vulnerabilities checked (`npm audit`)

### ✅ Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing of CLI commands
- [ ] Test installation works (`npm pack` and local install)

## 🚀 Release Process

### Step 1: Prepare Release

```bash
# Run the preparation script
npm run prepare-release

# This will:
# - Check NPM authentication
# - Validate package.json
# - Run tests
# - Test installation
# - Generate documentation
```

### Step 2: Version Management (with Changesets)

```bash
# Add a changeset (describe your changes)
npm run changeset

# Update version based on changesets
npm run version

# Review the updated version in package.json
```

### Step 3: Final Validation

```bash
# Test what will be published
npm run pack-test

# Verify the package contents
npm pack
tar -tzf task-master-ai-*.tgz
rm task-master-ai-*.tgz
```

### Step 4: Publish

```bash
# Publish to NPM
npm run release

# Or publish directly
npm publish

# For beta releases
npm publish --tag beta
```

### Step 5: Post-Release Verification

```bash
# Test the published package
npx task-master-ai@latest --version
npx task-master-ai@latest --help

# Test in a clean directory
mkdir test-install && cd test-install
npx task-master-ai models --setup
cd .. && rm -rf test-install
```

## 🔧 Manual Release Process (Alternative)

If not using changesets:

```bash
# 1. Update version
npm version patch  # or minor, major

# 2. Run tests
npm test

# 3. Publish
npm publish

# 4. Create git tag
git push --tags
```

## 📦 Package Contents Verification

The published package should include:

### Required Files
- [ ] `bin/task-master.js` (main executable)
- [ ] `scripts/` directory (core functionality)
- [ ] `src/` directory (AI providers, etc.)
- [ ] `mcp-server/` directory (MCP server)
- [ ] `package.json`
- [ ] `README.md` or `README-task-master.md`

### Optional Files
- [ ] `assets/` directory
- [ ] `.cursor/` directory
- [ ] `examples/` directory
- [ ] License files

### Files to Exclude
- [ ] `node_modules/` (automatically excluded)
- [ ] `.git/` (automatically excluded)
- [ ] `tests/` (should be excluded)
- [ ] `.env` files (should be excluded)
- [ ] Development configs

## 🧪 Testing Scenarios

### Local Testing
```bash
# Test direct execution
./bin/task-master.js --help

# Test through npm scripts
npm run mcp-server
```

### NPX Testing
```bash
# Test latest published version
npx task-master-ai@latest --version

# Test specific version
npx task-master-ai@0.16.2 --help

# Test in different directories
cd /tmp
npx task-master-ai models --setup
```

### Global Installation Testing
```bash
# Test global install
npm install -g task-master-ai

# Test global usage
task-master-ai --help
task-master models --setup

# Cleanup
npm uninstall -g task-master-ai
```

## 🔍 Common Issues and Solutions

### Issue: Binary not executable
```bash
# Solution: Add to package.json
"scripts": {
  "prepare": "chmod +x bin/task-master.js"
}
```

### Issue: Missing dependencies in production
```bash
# Check what's included
npm ls --production

# Move from devDependencies to dependencies if needed
```

### Issue: Large package size
```bash
# Check package size
npm pack --dry-run

# Optimize by excluding unnecessary files in .npmignore
```

### Issue: NPX cache issues
```bash
# Clear NPX cache
npx clear-npx-cache

# Or use specific version
npx task-master-ai@latest
```

## 📊 Release Metrics

After release, monitor:

- [ ] Download statistics on NPM
- [ ] GitHub issues/feedback
- [ ] User adoption metrics
- [ ] Performance in different environments

## 🔄 Post-Release Tasks

- [ ] Update GitHub release notes
- [ ] Update documentation
- [ ] Announce on relevant channels
- [ ] Monitor for issues
- [ ] Plan next release

## 📞 Support Channels

- GitHub Issues: https://github.com/eyaltoledano/claude-task-master/issues
- NPM Package: https://www.npmjs.com/package/task-master-ai
- Documentation: README files and examples/

## 🎯 Success Criteria

A successful release should:

1. ✅ Install without errors via NPX
2. ✅ Execute basic commands successfully
3. ✅ Work in different environments (Linux, macOS, Windows)
4. ✅ Have all dependencies resolved
5. ✅ Maintain backward compatibility
6. ✅ Include proper documentation
