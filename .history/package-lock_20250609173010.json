{
	"name": "task-master-ai",
<<<<<<< HEAD
	"version": "0.16.2-rc.0",
=======
	"version": "0.16.1",
>>>>>>> support-claude
	"lockfileVersion": 3,
	"requires": true,
	"packages": {
		"": {
			"name": "task-master-ai",
<<<<<<< HEAD
			"version": "0.16.2-rc.0",
=======
			"version": "0.16.1",
>>>>>>> support-claude
			"license": "MIT WITH Commons-Clause",
			"dependencies": {
				"@ai-sdk/amazon-bedrock": "^2.2.9",
				"@ai-sdk/anthropic": "^1.2.10",
				"@ai-sdk/azure": "^1.3.17",
				"@ai-sdk/google": "^1.2.13",
				"@ai-sdk/google-vertex": "^2.2.23",
				"@ai-sdk/mistral": "^1.2.7",
				"@ai-sdk/openai": "^1.3.20",
				"@ai-sdk/perplexity": "^1.1.7",
				"@ai-sdk/xai": "^1.2.15",
				"@anthropic-ai/sdk": "^0.39.0",
				"@aws-sdk/credential-providers": "^3.817.0",
				"@openrouter/ai-sdk-provider": "^0.4.5",
				"ai": "^4.3.10",
				"boxen": "^8.0.1",
				"chalk": "^5.4.1",
				"cli-table3": "^0.6.5",
				"commander": "^11.1.0",
				"cors": "^2.8.5",
				"dotenv": "^16.3.1",
				"express": "^4.21.2",
				"fastmcp": "^2.2.2",
				"figlet": "^1.8.0",
				"fuse.js": "^7.1.0",
				"gradient-string": "^3.0.0",
				"helmet": "^8.1.0",
				"inquirer": "^12.5.0",
				"jsonwebtoken": "^9.0.2",
				"lru-cache": "^10.2.0",
				"ollama-ai-provider": "^1.2.0",
				"openai": "^4.89.0",
				"ora": "^8.2.0",
				"uuid": "^11.1.0",
				"zod": "^3.23.8"
			},
			"bin": {
				"task-master": "bin/task-master.js",
				"task-master-ai": "mcp-server/server.js",
				"task-master-mcp": "mcp-server/server.js"
			},
			"devDependencies": {
				"@biomejs/biome": "^1.9.4",
				"@changesets/changelog-github": "^0.5.1",
				"@changesets/cli": "^2.28.1",
				"@types/jest": "^29.5.14",
				"execa": "^8.0.1",
				"ink": "^5.0.1",
				"jest": "^29.7.0",
				"jest-environment-node": "^29.7.0",
				"mock-fs": "^5.5.0",
				"prettier": "^3.5.3",
				"react": "^18.3.1",
				"supertest": "^7.1.0",
				"tsx": "^4.16.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@ai-sdk/amazon-bedrock": {
			"version": "2.2.9",
			"resolved": "https://registry.npmjs.org/@ai-sdk/amazon-bedrock/-/amazon-bedrock-2.2.9.tgz",
			"integrity": "sha512-c4IWCheWLJq7HhRhr0crlB6wqy8KuVj7hsO7pxl7KaYgCiRFkJA3q8Fv9rUJK4XjtOeFxDs6j2z3hVG62jMxDQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.8",
				"@smithy/eventstream-codec": "^4.0.1",
				"@smithy/util-utf8": "^4.0.0",
				"aws4fetch": "^1.0.20"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/amazon-bedrock/node_modules/@ai-sdk/provider-utils": {
			"version": "2.2.8",
			"resolved": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.8.tgz",
			"integrity": "sha512-fqhG+4sCVv8x7nFzYnFo19ryhAa3w096Kmc3hWxMQfW/TubPOmt3A6tYZhl4mUfQWWQMsuSkLrtjlWuXBVSGQA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"nanoid": "^3.3.8",
				"secure-json-parse": "^2.7.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.23.8"
			}
		},
		"node_modules/@ai-sdk/anthropic": {
			"version": "1.2.12",
			"resolved": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.12.tgz",
			"integrity": "sha512-YSzjlko7JvuiyQFmI9RN1tNZdEiZxc+6xld/0tq/VkJaHpEzGAb1yiNxxvmYVcjvfu/PcvCxAAYXmTYQQ63IHQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.8"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/anthropic/node_modules/@ai-sdk/provider-utils": {
			"version": "2.2.8",
			"resolved": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.8.tgz",
			"integrity": "sha512-fqhG+4sCVv8x7nFzYnFo19ryhAa3w096Kmc3hWxMQfW/TubPOmt3A6tYZhl4mUfQWWQMsuSkLrtjlWuXBVSGQA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"nanoid": "^3.3.8",
				"secure-json-parse": "^2.7.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.23.8"
			}
		},
		"node_modules/@ai-sdk/azure": {
			"version": "1.3.17",
			"resolved": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.17.tgz",
			"integrity": "sha512-uGCQ7q81S3mY1EmH2mrsysc/Qw9czMiNTJDr5fc5ocDnHS89rbiaNUdBbdYpjS471EEa2Rcrx2FTCGiQ0gTPDQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/openai": "1.3.16",
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/azure/node_modules/@ai-sdk/openai": {
			"version": "1.3.16",
			"resolved": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.16.tgz",
			"integrity": "sha512-pjtiBKt1GgaSKZryTbM3tqgoegJwgAUlp1+X5uN6T+VPnI4FLSymV65tyloWzDlyqZmi9HXnnSRPu76VoL5D5g==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/google": {
			"version": "1.2.18",
			"resolved": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.18.tgz",
			"integrity": "sha512-8B70+i+uB12Ae6Sn6B9Oc6W0W/XorGgc88Nx0pyUrcxFOdytHBaAVhTPqYsO3LLClfjYN8pQ9GMxd5cpGEnUcA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.8"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/google-vertex": {
			"version": "2.2.23",
			"resolved": "https://registry.npmjs.org/@ai-sdk/google-vertex/-/google-vertex-2.2.23.tgz",
			"integrity": "sha512-q8rOXqEYBOTcgVW1+QlkYEd+bP1ZWfRYhRs3QzzAL1SP3AQqltuKM7ovO53AFDq5hwlodFMHQ4v9WmXmibgjAg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/anthropic": "1.2.12",
				"@ai-sdk/google": "1.2.18",
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.8",
				"google-auth-library": "^9.15.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/google-vertex/node_modules/@ai-sdk/provider-utils": {
			"version": "2.2.8",
			"resolved": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.8.tgz",
			"integrity": "sha512-fqhG+4sCVv8x7nFzYnFo19ryhAa3w096Kmc3hWxMQfW/TubPOmt3A6tYZhl4mUfQWWQMsuSkLrtjlWuXBVSGQA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"nanoid": "^3.3.8",
				"secure-json-parse": "^2.7.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.23.8"
			}
		},
		"node_modules/@ai-sdk/google/node_modules/@ai-sdk/provider-utils": {
			"version": "2.2.8",
			"resolved": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.8.tgz",
			"integrity": "sha512-fqhG+4sCVv8x7nFzYnFo19ryhAa3w096Kmc3hWxMQfW/TubPOmt3A6tYZhl4mUfQWWQMsuSkLrtjlWuXBVSGQA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"nanoid": "^3.3.8",
				"secure-json-parse": "^2.7.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.23.8"
			}
		},
		"node_modules/@ai-sdk/mistral": {
			"version": "1.2.7",
			"resolved": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.7.tgz",
			"integrity": "sha512-MbOMGfnHKcsvjbv4d6OT7Oaz+Wp4jD8yityqC4hASoKoW1s7L52woz25ES8RgAgTRlfbEZ3MOxEzLu58I228bQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/openai": {
			"version": "1.3.20",
			"resolved": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.20.tgz",
			"integrity": "sha512-/DflUy7ROG9k6n6YTXMBFPbujBKnbGY58f3CwvicLvDar9nDAloVnUWd3LUoOxpSVnX8vtQ7ngxF52SLWO6RwQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/openai-compatible": {
			"version": "0.2.13",
			"resolved": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.13.tgz",
			"integrity": "sha512-tB+lL8Z3j0qDod/mvxwjrPhbLUHp/aQW+NvMoJaqeTtP+Vmv5qR800pncGczxn5WN0pllQm+7aIRDnm69XeSbg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/perplexity": {
			"version": "1.1.7",
			"resolved": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.7.tgz",
			"integrity": "sha512-FH2zEADLU/NTuRkQXMbZkUZ0qSsJ5qhufQ+7IsFMuhhKShGt0M8gOZlnkxuolnIjDrOdD3r1r59nZKMsFHuwqw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@ai-sdk/provider": {
			"version": "1.1.3",
			"resolved": "https://registry.npmjs.org/@ai-sdk/provider/-/provider-1.1.3.tgz",
			"integrity": "sha512-qZMxYJ0qqX/RfnuIaab+zp8UAeJn/ygXXAffR5I4N0n1IrvA6qBsjc8hXLmBiMV2zoXlifkacF7sEFnYnjBcqg==",
			"license": "Apache-2.0",
			"dependencies": {
				"json-schema": "^0.4.0"
			},
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@ai-sdk/provider-utils": {
			"version": "2.2.7",
			"resolved": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.7.tgz",
			"integrity": "sha512-kM0xS3GWg3aMChh9zfeM+80vEZfXzR3JEUBdycZLtbRZ2TRT8xOj3WodGHPb06sUK5yD7pAXC/P7ctsi2fvUGQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"nanoid": "^3.3.8",
				"secure-json-parse": "^2.7.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.23.8"
			}
		},
		"node_modules/@ai-sdk/react": {
			"version": "1.2.9",
			"resolved": "https://registry.npmjs.org/@ai-sdk/react/-/react-1.2.9.tgz",
			"integrity": "sha512-/VYm8xifyngaqFDLXACk/1czDRCefNCdALUyp+kIX6DUIYUWTM93ISoZ+qJ8+3E+FiJAKBQz61o8lIIl+vYtzg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider-utils": "2.2.7",
				"@ai-sdk/ui-utils": "1.2.8",
				"swr": "^2.2.5",
				"throttleit": "2.1.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"react": "^18 || ^19 || ^19.0.0-rc",
				"zod": "^3.23.8"
			},
			"peerDependenciesMeta": {
				"zod": {
					"optional": true
				}
			}
		},
		"node_modules/@ai-sdk/ui-utils": {
			"version": "1.2.8",
			"resolved": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.8.tgz",
			"integrity": "sha512-nls/IJCY+ks3Uj6G/agNhXqQeLVqhNfoJbuNgCny+nX2veY5ADB91EcZUqVeQ/ionul2SeUswPY6Q/DxteY29Q==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7",
				"zod-to-json-schema": "^3.24.1"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.23.8"
			}
		},
		"node_modules/@ai-sdk/xai": {
			"version": "1.2.15",
			"resolved": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.15.tgz",
			"integrity": "sha512-18qEYyVHIqTiOMePE00bfx4kJrTHM4dV3D3Rpe+eBISlY80X1FnzZRnRTJo3Q6MOSmW5+ZKVaX9jtryhoFpn0A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/openai-compatible": "0.2.13",
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@alcalzone/ansi-tokenize": {
			"version": "0.1.3",
			"resolved": "https://registry.npmjs.org/@alcalzone/ansi-tokenize/-/ansi-tokenize-0.1.3.tgz",
			"integrity": "sha512-3yWxPTq3UQ/FY9p1ErPxIyfT64elWaMvM9lIHnaqpyft63tkxodF5aUElYHrdisWve5cETkh1+KBw1yJuW0aRw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^6.2.1",
				"is-fullwidth-code-point": "^4.0.0"
			},
			"engines": {
				"node": ">=14.13.1"
			}
		},
		"node_modules/@alcalzone/ansi-tokenize/node_modules/ansi-styles": {
			"version": "6.2.1",
			"resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz",
			"integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/chalk/ansi-styles?sponsor=1"
			}
		},
		"node_modules/@alcalzone/ansi-tokenize/node_modules/is-fullwidth-code-point": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz",
			"integrity": "sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/@ampproject/remapping": {
			"version": "2.3.0",
			"resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz",
			"integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==",
			"dev": true,
			"license": "Apache-2.0",
			"dependencies": {
				"@jridgewell/gen-mapping": "^0.3.5",
				"@jridgewell/trace-mapping": "^0.3.24"
			},
			"engines": {
				"node": ">=6.0.0"
			}
		},
		"node_modules/@anthropic-ai/sdk": {
			"version": "0.39.0",
			"resolved": "https://registry.npmjs.org/@anthropic-ai/sdk/-/sdk-0.39.0.tgz",
			"integrity": "sha512-eMyDIPRZbt1CCLErRCi3exlAvNkBtRe+kW5vvJyef93PmNr/clstYgHhtvmkxN82nlKgzyGPCyGxrm0JQ1ZIdg==",
			"license": "MIT",
			"dependencies": {
				"@types/node": "^18.11.18",
				"@types/node-fetch": "^2.6.4",
				"abort-controller": "^3.0.0",
				"agentkeepalive": "^4.2.1",
				"form-data-encoder": "1.7.2",
				"formdata-node": "^4.3.2",
				"node-fetch": "^2.6.7"
			}
		},
		"node_modules/@anthropic-ai/sdk/node_modules/node-fetch": {
			"version": "2.7.0",
			"resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz",
			"integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==",
			"license": "MIT",
			"dependencies": {
				"whatwg-url": "^5.0.0"
			},
			"engines": {
				"node": "4.x || >=6.0.0"
			},
			"peerDependencies": {
				"encoding": "^0.1.0"
			},
			"peerDependenciesMeta": {
				"encoding": {
					"optional": true
				}
			}
		},
		"node_modules/@aws-crypto/crc32": {
			"version": "5.2.0",
			"resolved": "https://registry.npmjs.org/@aws-crypto/crc32/-/crc32-5.2.0.tgz",
			"integrity": "sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-crypto/util": "^5.2.0",
				"@aws-sdk/types": "^3.222.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=16.0.0"
			}
		},
		"node_modules/@aws-crypto/sha256-browser": {
			"version": "5.2.0",
			"resolved": "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-5.2.0.tgz",
			"integrity": "sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-crypto/sha256-js": "^5.2.0",
				"@aws-crypto/supports-web-crypto": "^5.2.0",
				"@aws-crypto/util": "^5.2.0",
				"@aws-sdk/types": "^3.222.0",
				"@aws-sdk/util-locate-window": "^3.0.0",
				"@smithy/util-utf8": "^2.0.0",
				"tslib": "^2.6.2"
			}
		},
		"node_modules/@aws-crypto/sha256-browser/node_modules/@smithy/is-array-buffer": {
			"version": "2.2.0",
			"resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-2.2.0.tgz",
			"integrity": "sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=14.0.0"
			}
		},
		"node_modules/@aws-crypto/sha256-browser/node_modules/@smithy/util-buffer-from": {
			"version": "2.2.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-2.2.0.tgz",
			"integrity": "sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/is-array-buffer": "^2.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=14.0.0"
			}
		},
		"node_modules/@aws-crypto/sha256-browser/node_modules/@smithy/util-utf8": {
			"version": "2.3.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-2.3.0.tgz",
			"integrity": "sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/util-buffer-from": "^2.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=14.0.0"
			}
		},
		"node_modules/@aws-crypto/sha256-js": {
			"version": "5.2.0",
			"resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-5.2.0.tgz",
			"integrity": "sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-crypto/util": "^5.2.0",
				"@aws-sdk/types": "^3.222.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=16.0.0"
			}
		},
		"node_modules/@aws-crypto/supports-web-crypto": {
			"version": "5.2.0",
			"resolved": "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-5.2.0.tgz",
			"integrity": "sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			}
		},
		"node_modules/@aws-crypto/util": {
			"version": "5.2.0",
			"resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-5.2.0.tgz",
			"integrity": "sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/types": "^3.222.0",
				"@smithy/util-utf8": "^2.0.0",
				"tslib": "^2.6.2"
			}
		},
		"node_modules/@aws-crypto/util/node_modules/@smithy/is-array-buffer": {
			"version": "2.2.0",
			"resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-2.2.0.tgz",
			"integrity": "sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=14.0.0"
			}
		},
		"node_modules/@aws-crypto/util/node_modules/@smithy/util-buffer-from": {
			"version": "2.2.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-2.2.0.tgz",
			"integrity": "sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/is-array-buffer": "^2.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=14.0.0"
			}
		},
		"node_modules/@aws-crypto/util/node_modules/@smithy/util-utf8": {
			"version": "2.3.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-2.3.0.tgz",
			"integrity": "sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/util-buffer-from": "^2.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=14.0.0"
			}
		},
		"node_modules/@aws-sdk/client-cognito-identity": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.817.0.tgz",
			"integrity": "sha512-MNGwOJDQU0jpvsLLPSuPQDhPtDzFTc/k7rLmiKoPrIlgb3Y8pSF4crpJ+ZH3+xod2NWyyOVMEMQeMaKFFdMaKw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-crypto/sha256-browser": "5.2.0",
				"@aws-crypto/sha256-js": "5.2.0",
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/credential-provider-node": "3.817.0",
				"@aws-sdk/middleware-host-header": "3.804.0",
				"@aws-sdk/middleware-logger": "3.804.0",
				"@aws-sdk/middleware-recursion-detection": "3.804.0",
				"@aws-sdk/middleware-user-agent": "3.816.0",
				"@aws-sdk/region-config-resolver": "3.808.0",
				"@aws-sdk/types": "3.804.0",
				"@aws-sdk/util-endpoints": "3.808.0",
				"@aws-sdk/util-user-agent-browser": "3.804.0",
				"@aws-sdk/util-user-agent-node": "3.816.0",
				"@smithy/config-resolver": "^4.1.2",
				"@smithy/core": "^3.3.3",
				"@smithy/fetch-http-handler": "^5.0.2",
				"@smithy/hash-node": "^4.0.2",
				"@smithy/invalid-dependency": "^4.0.2",
				"@smithy/middleware-content-length": "^4.0.2",
				"@smithy/middleware-endpoint": "^4.1.6",
				"@smithy/middleware-retry": "^4.1.7",
				"@smithy/middleware-serde": "^4.0.5",
				"@smithy/middleware-stack": "^4.0.2",
				"@smithy/node-config-provider": "^4.1.1",
				"@smithy/node-http-handler": "^4.0.4",
				"@smithy/protocol-http": "^5.1.0",
				"@smithy/smithy-client": "^4.2.6",
				"@smithy/types": "^4.2.0",
				"@smithy/url-parser": "^4.0.2",
				"@smithy/util-base64": "^4.0.0",
				"@smithy/util-body-length-browser": "^4.0.0",
				"@smithy/util-body-length-node": "^4.0.0",
				"@smithy/util-defaults-mode-browser": "^4.0.14",
				"@smithy/util-defaults-mode-node": "^4.0.14",
				"@smithy/util-endpoints": "^3.0.4",
				"@smithy/util-middleware": "^4.0.2",
				"@smithy/util-retry": "^4.0.3",
				"@smithy/util-utf8": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/client-sso": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.817.0.tgz",
			"integrity": "sha512-fCh5rUHmWmWDvw70NNoWpE5+BRdtNi45kDnIoeoszqVg7UKF79SlG+qYooUT52HKCgDNHqgbWaXxMOSqd2I/OQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-crypto/sha256-browser": "5.2.0",
				"@aws-crypto/sha256-js": "5.2.0",
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/middleware-host-header": "3.804.0",
				"@aws-sdk/middleware-logger": "3.804.0",
				"@aws-sdk/middleware-recursion-detection": "3.804.0",
				"@aws-sdk/middleware-user-agent": "3.816.0",
				"@aws-sdk/region-config-resolver": "3.808.0",
				"@aws-sdk/types": "3.804.0",
				"@aws-sdk/util-endpoints": "3.808.0",
				"@aws-sdk/util-user-agent-browser": "3.804.0",
				"@aws-sdk/util-user-agent-node": "3.816.0",
				"@smithy/config-resolver": "^4.1.2",
				"@smithy/core": "^3.3.3",
				"@smithy/fetch-http-handler": "^5.0.2",
				"@smithy/hash-node": "^4.0.2",
				"@smithy/invalid-dependency": "^4.0.2",
				"@smithy/middleware-content-length": "^4.0.2",
				"@smithy/middleware-endpoint": "^4.1.6",
				"@smithy/middleware-retry": "^4.1.7",
				"@smithy/middleware-serde": "^4.0.5",
				"@smithy/middleware-stack": "^4.0.2",
				"@smithy/node-config-provider": "^4.1.1",
				"@smithy/node-http-handler": "^4.0.4",
				"@smithy/protocol-http": "^5.1.0",
				"@smithy/smithy-client": "^4.2.6",
				"@smithy/types": "^4.2.0",
				"@smithy/url-parser": "^4.0.2",
				"@smithy/util-base64": "^4.0.0",
				"@smithy/util-body-length-browser": "^4.0.0",
				"@smithy/util-body-length-node": "^4.0.0",
				"@smithy/util-defaults-mode-browser": "^4.0.14",
				"@smithy/util-defaults-mode-node": "^4.0.14",
				"@smithy/util-endpoints": "^3.0.4",
				"@smithy/util-middleware": "^4.0.2",
				"@smithy/util-retry": "^4.0.3",
				"@smithy/util-utf8": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/core": {
			"version": "3.816.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/core/-/core-3.816.0.tgz",
			"integrity": "sha512-Lx50wjtyarzKpMFV6V+gjbSZDgsA/71iyifbClGUSiNPoIQ4OCV0KVOmAAj7mQRVvGJqUMWKVM+WzK79CjbjWA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/types": "3.804.0",
				"@smithy/core": "^3.3.3",
				"@smithy/node-config-provider": "^4.1.1",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/protocol-http": "^5.1.0",
				"@smithy/signature-v4": "^5.1.0",
				"@smithy/smithy-client": "^4.2.6",
				"@smithy/types": "^4.2.0",
				"@smithy/util-middleware": "^4.0.2",
				"fast-xml-parser": "4.4.1",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-provider-cognito-identity": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.817.0.tgz",
			"integrity": "sha512-+dzgWGmdmMNDdeSF+VvONN+hwqoGKX5A6Z3+siMO4CIoKWN7u5nDOx/JLjTGdVQji3522pJjJ+o9veQJNWOMRg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/client-cognito-identity": "3.817.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-provider-env": {
			"version": "3.816.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.816.0.tgz",
			"integrity": "sha512-wUJZwRLe+SxPxRV9AENYBLrJZRrNIo+fva7ZzejsC83iz7hdfq6Rv6B/aHEdPwG/nQC4+q7UUvcRPlomyrpsBA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-provider-http": {
			"version": "3.816.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-http/-/credential-provider-http-3.816.0.tgz",
			"integrity": "sha512-gcWGzMQ7yRIF+ljTkR8Vzp7727UY6cmeaPrFQrvcFB8PhOqWpf7g0JsgOf5BSaP8CkkSQcTQHc0C5ZYAzUFwPg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/fetch-http-handler": "^5.0.2",
				"@smithy/node-http-handler": "^4.0.4",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/protocol-http": "^5.1.0",
				"@smithy/smithy-client": "^4.2.6",
				"@smithy/types": "^4.2.0",
				"@smithy/util-stream": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-provider-ini": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.817.0.tgz",
			"integrity": "sha512-kyEwbQyuXE+phWVzloMdkFv6qM6NOon+asMXY5W0fhDKwBz9zQLObDRWBrvQX9lmqq8BbDL1sCfZjOh82Y+RFw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/credential-provider-env": "3.816.0",
				"@aws-sdk/credential-provider-http": "3.816.0",
				"@aws-sdk/credential-provider-process": "3.816.0",
				"@aws-sdk/credential-provider-sso": "3.817.0",
				"@aws-sdk/credential-provider-web-identity": "3.817.0",
				"@aws-sdk/nested-clients": "3.817.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/credential-provider-imds": "^4.0.4",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/shared-ini-file-loader": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-provider-node": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.817.0.tgz",
			"integrity": "sha512-b5mz7av0Lhavs1Bz3Zb+jrs0Pki93+8XNctnVO0drBW98x1fM4AR38cWvGbM/w9F9Q0/WEH3TinkmrMPrP4T/w==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/credential-provider-env": "3.816.0",
				"@aws-sdk/credential-provider-http": "3.816.0",
				"@aws-sdk/credential-provider-ini": "3.817.0",
				"@aws-sdk/credential-provider-process": "3.816.0",
				"@aws-sdk/credential-provider-sso": "3.817.0",
				"@aws-sdk/credential-provider-web-identity": "3.817.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/credential-provider-imds": "^4.0.4",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/shared-ini-file-loader": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-provider-process": {
			"version": "3.816.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.816.0.tgz",
			"integrity": "sha512-9Tm+AxMoV2Izvl5b9tyMQRbBwaex8JP06HN7ZeCXgC5sAsSN+o8dsThnEhf8jKN+uBpT6CLWKN1TXuUMrAmW1A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/shared-ini-file-loader": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-provider-sso": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.817.0.tgz",
			"integrity": "sha512-gFUAW3VmGvdnueK1bh6TOcRX+j99Xm0men1+gz3cA4RE+rZGNy1Qjj8YHlv0hPwI9OnTPZquvPzA5fkviGREWg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/client-sso": "3.817.0",
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/token-providers": "3.817.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/shared-ini-file-loader": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-provider-web-identity": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.817.0.tgz",
			"integrity": "sha512-A2kgkS9g6NY0OMT2f2EdXHpL17Ym81NhbGnQ8bRXPqESIi7TFypFD2U6osB2VnsFv+MhwM+Ke4PKXSmLun22/A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/nested-clients": "3.817.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/credential-providers": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/credential-providers/-/credential-providers-3.817.0.tgz",
			"integrity": "sha512-i6Q2MyktWHG4YG+EmLlnXTgNVjW9/yeNHSKzF55GTho5fjqfU+t9beJfuMWclanRCifamm3N5e5OCm52rVDdTQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/client-cognito-identity": "3.817.0",
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/credential-provider-cognito-identity": "3.817.0",
				"@aws-sdk/credential-provider-env": "3.816.0",
				"@aws-sdk/credential-provider-http": "3.816.0",
				"@aws-sdk/credential-provider-ini": "3.817.0",
				"@aws-sdk/credential-provider-node": "3.817.0",
				"@aws-sdk/credential-provider-process": "3.816.0",
				"@aws-sdk/credential-provider-sso": "3.817.0",
				"@aws-sdk/credential-provider-web-identity": "3.817.0",
				"@aws-sdk/nested-clients": "3.817.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/config-resolver": "^4.1.2",
				"@smithy/core": "^3.3.3",
				"@smithy/credential-provider-imds": "^4.0.4",
				"@smithy/node-config-provider": "^4.1.1",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/middleware-host-header": {
			"version": "3.804.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.804.0.tgz",
			"integrity": "sha512-bum1hLVBrn2lJCi423Z2fMUYtsbkGI2s4N+2RI2WSjvbaVyMSv/WcejIrjkqiiMR+2Y7m5exgoKeg4/TODLDPQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/types": "3.804.0",
				"@smithy/protocol-http": "^5.1.0",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/middleware-logger": {
			"version": "3.804.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.804.0.tgz",
			"integrity": "sha512-w/qLwL3iq0KOPQNat0Kb7sKndl9BtceigINwBU7SpkYWX9L/Lem6f8NPEKrC9Tl4wDBht3Yztub4oRTy/horJA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/types": "3.804.0",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/middleware-recursion-detection": {
			"version": "3.804.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.804.0.tgz",
			"integrity": "sha512-zqHOrvLRdsUdN/ehYfZ9Tf8svhbiLLz5VaWUz22YndFv6m9qaAcijkpAOlKexsv3nLBMJdSdJ6GUTAeIy3BZzw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/types": "3.804.0",
				"@smithy/protocol-http": "^5.1.0",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/middleware-user-agent": {
			"version": "3.816.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.816.0.tgz",
			"integrity": "sha512-bHRSlWZ0xDsFR8E2FwDb//0Ff6wMkVx4O+UKsfyNlAbtqCiiHRt5ANNfKPafr95cN2CCxLxiPvFTFVblQM5TsQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/types": "3.804.0",
				"@aws-sdk/util-endpoints": "3.808.0",
				"@smithy/core": "^3.3.3",
				"@smithy/protocol-http": "^5.1.0",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/nested-clients": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/nested-clients/-/nested-clients-3.817.0.tgz",
			"integrity": "sha512-vQ2E06A48STJFssueJQgxYD8lh1iGJoLJnHdshRDWOQb8gy1wVQR+a7MkPGhGR6lGoS0SCnF/Qp6CZhnwLsqsQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-crypto/sha256-browser": "5.2.0",
				"@aws-crypto/sha256-js": "5.2.0",
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/middleware-host-header": "3.804.0",
				"@aws-sdk/middleware-logger": "3.804.0",
				"@aws-sdk/middleware-recursion-detection": "3.804.0",
				"@aws-sdk/middleware-user-agent": "3.816.0",
				"@aws-sdk/region-config-resolver": "3.808.0",
				"@aws-sdk/types": "3.804.0",
				"@aws-sdk/util-endpoints": "3.808.0",
				"@aws-sdk/util-user-agent-browser": "3.804.0",
				"@aws-sdk/util-user-agent-node": "3.816.0",
				"@smithy/config-resolver": "^4.1.2",
				"@smithy/core": "^3.3.3",
				"@smithy/fetch-http-handler": "^5.0.2",
				"@smithy/hash-node": "^4.0.2",
				"@smithy/invalid-dependency": "^4.0.2",
				"@smithy/middleware-content-length": "^4.0.2",
				"@smithy/middleware-endpoint": "^4.1.6",
				"@smithy/middleware-retry": "^4.1.7",
				"@smithy/middleware-serde": "^4.0.5",
				"@smithy/middleware-stack": "^4.0.2",
				"@smithy/node-config-provider": "^4.1.1",
				"@smithy/node-http-handler": "^4.0.4",
				"@smithy/protocol-http": "^5.1.0",
				"@smithy/smithy-client": "^4.2.6",
				"@smithy/types": "^4.2.0",
				"@smithy/url-parser": "^4.0.2",
				"@smithy/util-base64": "^4.0.0",
				"@smithy/util-body-length-browser": "^4.0.0",
				"@smithy/util-body-length-node": "^4.0.0",
				"@smithy/util-defaults-mode-browser": "^4.0.14",
				"@smithy/util-defaults-mode-node": "^4.0.14",
				"@smithy/util-endpoints": "^3.0.4",
				"@smithy/util-middleware": "^4.0.2",
				"@smithy/util-retry": "^4.0.3",
				"@smithy/util-utf8": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/region-config-resolver": {
			"version": "3.808.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.808.0.tgz",
			"integrity": "sha512-9x2QWfphkARZY5OGkl9dJxZlSlYM2l5inFeo2bKntGuwg4A4YUe5h7d5yJ6sZbam9h43eBrkOdumx03DAkQF9A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/types": "3.804.0",
				"@smithy/node-config-provider": "^4.1.1",
				"@smithy/types": "^4.2.0",
				"@smithy/util-config-provider": "^4.0.0",
				"@smithy/util-middleware": "^4.0.2",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/token-providers": {
			"version": "3.817.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.817.0.tgz",
			"integrity": "sha512-CYN4/UO0VaqyHf46ogZzNrVX7jI3/CfiuktwKlwtpKA6hjf2+ivfgHSKzPpgPBcSEfiibA/26EeLuMnB6cpSrQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/core": "3.816.0",
				"@aws-sdk/nested-clients": "3.817.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/property-provider": "^4.0.2",
				"@smithy/shared-ini-file-loader": "^4.0.2",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/types": {
			"version": "3.804.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.804.0.tgz",
			"integrity": "sha512-A9qnsy9zQ8G89vrPPlNG9d1d8QcKRGqJKqwyGgS0dclJpwy6d1EWgQLIolKPl6vcFpLoe6avLOLxr+h8ur5wpg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/util-endpoints": {
			"version": "3.808.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.808.0.tgz",
			"integrity": "sha512-N6Lic98uc4ADB7fLWlzx+1uVnq04VgVjngZvwHoujcRg9YDhIg9dUDiTzD5VZv13g1BrPYmvYP1HhsildpGV6w==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/types": "3.804.0",
				"@smithy/types": "^4.2.0",
				"@smithy/util-endpoints": "^3.0.4",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/util-locate-window": {
			"version": "3.804.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.804.0.tgz",
			"integrity": "sha512-zVoRfpmBVPodYlnMjgVjfGoEZagyRF5IPn3Uo6ZvOZp24chnW/FRstH7ESDHDDRga4z3V+ElUQHKpFDXWyBW5A==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@aws-sdk/util-user-agent-browser": {
			"version": "3.804.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.804.0.tgz",
			"integrity": "sha512-KfW6T6nQHHM/vZBBdGn6fMyG/MgX5lq82TDdX4HRQRRuHKLgBWGpKXqqvBwqIaCdXwWHgDrg2VQups6GqOWW2A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/types": "3.804.0",
				"@smithy/types": "^4.2.0",
				"bowser": "^2.11.0",
				"tslib": "^2.6.2"
			}
		},
		"node_modules/@aws-sdk/util-user-agent-node": {
			"version": "3.816.0",
			"resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.816.0.tgz",
			"integrity": "sha512-Q6dxmuj4hL7pudhrneWEQ7yVHIQRBFr0wqKLF1opwOi1cIePuoEbPyJ2jkel6PDEv1YMfvsAKaRshp6eNA8VHg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-sdk/middleware-user-agent": "3.816.0",
				"@aws-sdk/types": "3.804.0",
				"@smithy/node-config-provider": "^4.1.1",
				"@smithy/types": "^4.2.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			},
			"peerDependencies": {
				"aws-crt": ">=1.0.0"
			},
			"peerDependenciesMeta": {
				"aws-crt": {
					"optional": true
				}
			}
		},
		"node_modules/@babel/code-frame": {
			"version": "7.26.2",
			"resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz",
			"integrity": "sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-validator-identifier": "^7.25.9",
				"js-tokens": "^4.0.0",
				"picocolors": "^1.0.0"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/compat-data": {
			"version": "7.26.8",
			"resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz",
			"integrity": "sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/core": {
			"version": "7.26.10",
			"resolved": "https://registry.npmjs.org/@babel/core/-/core-7.26.10.tgz",
			"integrity": "sha512-vMqyb7XCDMPvJFFOaT9kxtiRh42GwlZEg1/uIgtZshS5a/8OaduUfCi7kynKgc3Tw/6Uo2D+db9qBttghhmxwQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@ampproject/remapping": "^2.2.0",
				"@babel/code-frame": "^7.26.2",
				"@babel/generator": "^7.26.10",
				"@babel/helper-compilation-targets": "^7.26.5",
				"@babel/helper-module-transforms": "^7.26.0",
				"@babel/helpers": "^7.26.10",
				"@babel/parser": "^7.26.10",
				"@babel/template": "^7.26.9",
				"@babel/traverse": "^7.26.10",
				"@babel/types": "^7.26.10",
				"convert-source-map": "^2.0.0",
				"debug": "^4.1.0",
				"gensync": "^1.0.0-beta.2",
				"json5": "^2.2.3",
				"semver": "^6.3.1"
			},
			"engines": {
				"node": ">=6.9.0"
			},
			"funding": {
				"type": "opencollective",
				"url": "https://opencollective.com/babel"
			}
		},
		"node_modules/@babel/generator": {
			"version": "7.26.10",
			"resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.10.tgz",
			"integrity": "sha512-rRHT8siFIXQrAYOYqZQVsAr8vJ+cBNqcVAY6m5V8/4QqzaPl+zDBe6cLEPRDuNOUf3ww8RfJVlOyQMoSI+5Ang==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/parser": "^7.26.10",
				"@babel/types": "^7.26.10",
				"@jridgewell/gen-mapping": "^0.3.5",
				"@jridgewell/trace-mapping": "^0.3.25",
				"jsesc": "^3.0.2"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/helper-compilation-targets": {
			"version": "7.26.5",
			"resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz",
			"integrity": "sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/compat-data": "^7.26.5",
				"@babel/helper-validator-option": "^7.25.9",
				"browserslist": "^4.24.0",
				"lru-cache": "^5.1.1",
				"semver": "^6.3.1"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {
			"version": "5.1.1",
			"resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz",
			"integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"yallist": "^3.0.2"
			}
		},
		"node_modules/@babel/helper-module-imports": {
			"version": "7.25.9",
			"resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz",
			"integrity": "sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/traverse": "^7.25.9",
				"@babel/types": "^7.25.9"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/helper-module-transforms": {
			"version": "7.26.0",
			"resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz",
			"integrity": "sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-module-imports": "^7.25.9",
				"@babel/helper-validator-identifier": "^7.25.9",
				"@babel/traverse": "^7.25.9"
			},
			"engines": {
				"node": ">=6.9.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0"
			}
		},
		"node_modules/@babel/helper-plugin-utils": {
			"version": "7.26.5",
			"resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz",
			"integrity": "sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/helper-string-parser": {
			"version": "7.25.9",
			"resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz",
			"integrity": "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/helper-validator-identifier": {
			"version": "7.25.9",
			"resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz",
			"integrity": "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/helper-validator-option": {
			"version": "7.25.9",
			"resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz",
			"integrity": "sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/helpers": {
			"version": "7.26.10",
			"resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.26.10.tgz",
			"integrity": "sha512-UPYc3SauzZ3JGgj87GgZ89JVdC5dj0AoetR5Bw6wj4niittNyFh6+eOGonYvJ1ao6B8lEa3Q3klS7ADZ53bc5g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/template": "^7.26.9",
				"@babel/types": "^7.26.10"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/parser": {
			"version": "7.26.10",
			"resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.10.tgz",
			"integrity": "sha512-6aQR2zGE/QFi8JpDLjUZEPYOs7+mhKXm86VaKFiLP35JQwQb6bwUE+XbvkH0EptsYhbNBSUGaUBLKqxH1xSgsA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/types": "^7.26.10"
			},
			"bin": {
				"parser": "bin/babel-parser.js"
			},
			"engines": {
				"node": ">=6.0.0"
			}
		},
		"node_modules/@babel/plugin-syntax-async-generators": {
			"version": "7.8.4",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz",
			"integrity": "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.8.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-bigint": {
			"version": "7.8.3",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz",
			"integrity": "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.8.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-class-properties": {
			"version": "7.12.13",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz",
			"integrity": "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.12.13"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-class-static-block": {
			"version": "7.14.5",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz",
			"integrity": "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.14.5"
			},
			"engines": {
				"node": ">=6.9.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-import-attributes": {
			"version": "7.26.0",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.26.0.tgz",
			"integrity": "sha512-e2dttdsJ1ZTpi3B9UYGLw41hifAubg19AtCu/2I/F1QNVclOBr1dYpTdmdyZ84Xiz43BS/tCUkMAZNLv12Pi+A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.25.9"
			},
			"engines": {
				"node": ">=6.9.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-import-meta": {
			"version": "7.10.4",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz",
			"integrity": "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.10.4"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-json-strings": {
			"version": "7.8.3",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz",
			"integrity": "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.8.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-jsx": {
			"version": "7.25.9",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.25.9.tgz",
			"integrity": "sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.25.9"
			},
			"engines": {
				"node": ">=6.9.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-logical-assignment-operators": {
			"version": "7.10.4",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz",
			"integrity": "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.10.4"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {
			"version": "7.8.3",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz",
			"integrity": "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.8.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-numeric-separator": {
			"version": "7.10.4",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz",
			"integrity": "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.10.4"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-object-rest-spread": {
			"version": "7.8.3",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz",
			"integrity": "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.8.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-optional-catch-binding": {
			"version": "7.8.3",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz",
			"integrity": "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.8.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-optional-chaining": {
			"version": "7.8.3",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz",
			"integrity": "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.8.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-private-property-in-object": {
			"version": "7.14.5",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz",
			"integrity": "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.14.5"
			},
			"engines": {
				"node": ">=6.9.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-top-level-await": {
			"version": "7.14.5",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz",
			"integrity": "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.14.5"
			},
			"engines": {
				"node": ">=6.9.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/plugin-syntax-typescript": {
			"version": "7.25.9",
			"resolved": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz",
			"integrity": "sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.25.9"
			},
			"engines": {
				"node": ">=6.9.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0-0"
			}
		},
		"node_modules/@babel/runtime": {
			"version": "7.27.0",
			"resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.0.tgz",
			"integrity": "sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"regenerator-runtime": "^0.14.0"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/template": {
			"version": "7.26.9",
			"resolved": "https://registry.npmjs.org/@babel/template/-/template-7.26.9.tgz",
			"integrity": "sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/code-frame": "^7.26.2",
				"@babel/parser": "^7.26.9",
				"@babel/types": "^7.26.9"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/traverse": {
			"version": "7.26.10",
			"resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.26.10.tgz",
			"integrity": "sha512-k8NuDrxr0WrPH5Aupqb2LCVURP/S0vBEn5mK6iH+GIYob66U5EtoZvcdudR2jQ4cmTwhEwW1DLB+Yyas9zjF6A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/code-frame": "^7.26.2",
				"@babel/generator": "^7.26.10",
				"@babel/parser": "^7.26.10",
				"@babel/template": "^7.26.9",
				"@babel/types": "^7.26.10",
				"debug": "^4.3.1",
				"globals": "^11.1.0"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@babel/types": {
			"version": "7.26.10",
			"resolved": "https://registry.npmjs.org/@babel/types/-/types-7.26.10.tgz",
			"integrity": "sha512-emqcG3vHrpxUKTrxcblR36dcrcoRDvKmnL/dCL6ZsHaShW80qxCAcNhzQZrpeM765VzEos+xOi4s+r4IXzTwdQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/helper-string-parser": "^7.25.9",
				"@babel/helper-validator-identifier": "^7.25.9"
			},
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/@bcoe/v8-coverage": {
			"version": "0.2.3",
			"resolved": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz",
			"integrity": "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@biomejs/biome": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/biome/-/biome-1.9.4.tgz",
			"integrity": "sha512-1rkd7G70+o9KkTn5KLmDYXihGoTaIGO9PIIN2ZB7UJxFrWw04CZHPYiMRjYsaDvVV7hP1dYNRLxSANLaBFGpog==",
			"dev": true,
			"hasInstallScript": true,
			"license": "MIT OR Apache-2.0",
			"bin": {
				"biome": "bin/biome"
			},
			"engines": {
				"node": ">=14.21.3"
			},
			"funding": {
				"type": "opencollective",
				"url": "https://opencollective.com/biome"
			},
			"optionalDependencies": {
				"@biomejs/cli-darwin-arm64": "1.9.4",
				"@biomejs/cli-darwin-x64": "1.9.4",
				"@biomejs/cli-linux-arm64": "1.9.4",
				"@biomejs/cli-linux-arm64-musl": "1.9.4",
				"@biomejs/cli-linux-x64": "1.9.4",
				"@biomejs/cli-linux-x64-musl": "1.9.4",
				"@biomejs/cli-win32-arm64": "1.9.4",
				"@biomejs/cli-win32-x64": "1.9.4"
			}
		},
		"node_modules/@biomejs/cli-darwin-arm64": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/cli-darwin-arm64/-/cli-darwin-arm64-1.9.4.tgz",
			"integrity": "sha512-bFBsPWrNvkdKrNCYeAp+xo2HecOGPAy9WyNyB/jKnnedgzl4W4Hb9ZMzYNbf8dMCGmUdSavlYHiR01QaYR58cw==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT OR Apache-2.0",
			"optional": true,
			"os": [
				"darwin"
			],
			"engines": {
				"node": ">=14.21.3"
			}
		},
		"node_modules/@biomejs/cli-darwin-x64": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/cli-darwin-x64/-/cli-darwin-x64-1.9.4.tgz",
			"integrity": "sha512-ngYBh/+bEedqkSevPVhLP4QfVPCpb+4BBe2p7Xs32dBgs7rh9nY2AIYUL6BgLw1JVXV8GlpKmb/hNiuIxfPfZg==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT OR Apache-2.0",
			"optional": true,
			"os": [
				"darwin"
			],
			"engines": {
				"node": ">=14.21.3"
			}
		},
		"node_modules/@biomejs/cli-linux-arm64": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/cli-linux-arm64/-/cli-linux-arm64-1.9.4.tgz",
			"integrity": "sha512-fJIW0+LYujdjUgJJuwesP4EjIBl/N/TcOX3IvIHJQNsAqvV2CHIogsmA94BPG6jZATS4Hi+xv4SkBBQSt1N4/g==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT OR Apache-2.0",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=14.21.3"
			}
		},
		"node_modules/@biomejs/cli-linux-arm64-musl": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/cli-linux-arm64-musl/-/cli-linux-arm64-musl-1.9.4.tgz",
			"integrity": "sha512-v665Ct9WCRjGa8+kTr0CzApU0+XXtRgwmzIf1SeKSGAv+2scAlW6JR5PMFo6FzqqZ64Po79cKODKf3/AAmECqA==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT OR Apache-2.0",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=14.21.3"
			}
		},
		"node_modules/@biomejs/cli-linux-x64": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/cli-linux-x64/-/cli-linux-x64-1.9.4.tgz",
			"integrity": "sha512-lRCJv/Vi3Vlwmbd6K+oQ0KhLHMAysN8lXoCI7XeHlxaajk06u7G+UsFSO01NAs5iYuWKmVZjmiOzJ0OJmGsMwg==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT OR Apache-2.0",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=14.21.3"
			}
		},
		"node_modules/@biomejs/cli-linux-x64-musl": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/cli-linux-x64-musl/-/cli-linux-x64-musl-1.9.4.tgz",
			"integrity": "sha512-gEhi/jSBhZ2m6wjV530Yy8+fNqG8PAinM3oV7CyO+6c3CEh16Eizm21uHVsyVBEB6RIM8JHIl6AGYCv6Q6Q9Tg==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT OR Apache-2.0",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=14.21.3"
			}
		},
		"node_modules/@biomejs/cli-win32-arm64": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/cli-win32-arm64/-/cli-win32-arm64-1.9.4.tgz",
			"integrity": "sha512-tlbhLk+WXZmgwoIKwHIHEBZUwxml7bRJgk0X2sPyNR3S93cdRq6XulAZRQJ17FYGGzWne0fgrXBKpl7l4M87Hg==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT OR Apache-2.0",
			"optional": true,
			"os": [
				"win32"
			],
			"engines": {
				"node": ">=14.21.3"
			}
		},
		"node_modules/@biomejs/cli-win32-x64": {
			"version": "1.9.4",
			"resolved": "https://registry.npmjs.org/@biomejs/cli-win32-x64/-/cli-win32-x64-1.9.4.tgz",
			"integrity": "sha512-8Y5wMhVIPaWe6jw2H+KlEm4wP/f7EW3810ZLmDlrEEy5KvBsb9ECEfu/kMWD484ijfQ8+nIi0giMgu9g1UAuuA==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT OR Apache-2.0",
			"optional": true,
			"os": [
				"win32"
			],
			"engines": {
				"node": ">=14.21.3"
			}
		},
		"node_modules/@changesets/apply-release-plan": {
			"version": "7.0.10",
			"resolved": "https://registry.npmjs.org/@changesets/apply-release-plan/-/apply-release-plan-7.0.10.tgz",
			"integrity": "sha512-wNyeIJ3yDsVspYvHnEz1xQDq18D9ifed3lI+wxRQRK4pArUcuHgCTrHv0QRnnwjhVCQACxZ+CBih3wgOct6UXw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/config": "^3.1.1",
				"@changesets/get-version-range-type": "^0.4.0",
				"@changesets/git": "^3.0.2",
				"@changesets/should-skip-package": "^0.1.2",
				"@changesets/types": "^6.1.0",
				"@manypkg/get-packages": "^1.1.3",
				"detect-indent": "^6.0.0",
				"fs-extra": "^7.0.1",
				"lodash.startcase": "^4.4.0",
				"outdent": "^0.5.0",
				"prettier": "^2.7.1",
				"resolve-from": "^5.0.0",
				"semver": "^7.5.3"
			}
		},
		"node_modules/@changesets/apply-release-plan/node_modules/prettier": {
			"version": "2.8.8",
			"resolved": "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz",
			"integrity": "sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==",
			"dev": true,
			"license": "MIT",
			"bin": {
				"prettier": "bin-prettier.js"
			},
			"engines": {
				"node": ">=10.13.0"
			},
			"funding": {
				"url": "https://github.com/prettier/prettier?sponsor=1"
			}
		},
		"node_modules/@changesets/apply-release-plan/node_modules/semver": {
			"version": "7.7.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
			"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
			"dev": true,
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/@changesets/assemble-release-plan": {
			"version": "6.0.6",
			"resolved": "https://registry.npmjs.org/@changesets/assemble-release-plan/-/assemble-release-plan-6.0.6.tgz",
			"integrity": "sha512-Frkj8hWJ1FRZiY3kzVCKzS0N5mMwWKwmv9vpam7vt8rZjLL1JMthdh6pSDVSPumHPshTTkKZ0VtNbE0cJHZZUg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/errors": "^0.2.0",
				"@changesets/get-dependents-graph": "^2.1.3",
				"@changesets/should-skip-package": "^0.1.2",
				"@changesets/types": "^6.1.0",
				"@manypkg/get-packages": "^1.1.3",
				"semver": "^7.5.3"
			}
		},
		"node_modules/@changesets/assemble-release-plan/node_modules/semver": {
			"version": "7.7.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
			"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
			"dev": true,
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/@changesets/changelog-git": {
			"version": "0.2.1",
			"resolved": "https://registry.npmjs.org/@changesets/changelog-git/-/changelog-git-0.2.1.tgz",
			"integrity": "sha512-x/xEleCFLH28c3bQeQIyeZf8lFXyDFVn1SgcBiR2Tw/r4IAWlk1fzxCEZ6NxQAjF2Nwtczoen3OA2qR+UawQ8Q==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/types": "^6.1.0"
			}
		},
		"node_modules/@changesets/changelog-github": {
			"version": "0.5.1",
			"resolved": "https://registry.npmjs.org/@changesets/changelog-github/-/changelog-github-0.5.1.tgz",
			"integrity": "sha512-BVuHtF+hrhUScSoHnJwTELB4/INQxVFc+P/Qdt20BLiBFIHFJDDUaGsZw+8fQeJTRP5hJZrzpt3oZWh0G19rAQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/get-github-info": "^0.6.0",
				"@changesets/types": "^6.1.0",
				"dotenv": "^8.1.0"
			}
		},
		"node_modules/@changesets/changelog-github/node_modules/dotenv": {
			"version": "8.6.0",
			"resolved": "https://registry.npmjs.org/dotenv/-/dotenv-8.6.0.tgz",
			"integrity": "sha512-IrPdXQsk2BbzvCBGBOTmmSH5SodmqZNt4ERAZDmW4CT+tL8VtvinqywuANaFu4bOMWki16nqf0e4oC0QIaDr/g==",
			"dev": true,
			"license": "BSD-2-Clause",
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/@changesets/cli": {
			"version": "2.28.1",
			"resolved": "https://registry.npmjs.org/@changesets/cli/-/cli-2.28.1.tgz",
			"integrity": "sha512-PiIyGRmSc6JddQJe/W1hRPjiN4VrMvb2VfQ6Uydy2punBioQrsxppyG5WafinKcW1mT0jOe/wU4k9Zy5ff21AA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/apply-release-plan": "^7.0.10",
				"@changesets/assemble-release-plan": "^6.0.6",
				"@changesets/changelog-git": "^0.2.1",
				"@changesets/config": "^3.1.1",
				"@changesets/errors": "^0.2.0",
				"@changesets/get-dependents-graph": "^2.1.3",
				"@changesets/get-release-plan": "^4.0.8",
				"@changesets/git": "^3.0.2",
				"@changesets/logger": "^0.1.1",
				"@changesets/pre": "^2.0.2",
				"@changesets/read": "^0.6.3",
				"@changesets/should-skip-package": "^0.1.2",
				"@changesets/types": "^6.1.0",
				"@changesets/write": "^0.4.0",
				"@manypkg/get-packages": "^1.1.3",
				"ansi-colors": "^4.1.3",
				"ci-info": "^3.7.0",
				"enquirer": "^2.4.1",
				"external-editor": "^3.1.0",
				"fs-extra": "^7.0.1",
				"mri": "^1.2.0",
				"p-limit": "^2.2.0",
				"package-manager-detector": "^0.2.0",
				"picocolors": "^1.1.0",
				"resolve-from": "^5.0.0",
				"semver": "^7.5.3",
				"spawndamnit": "^3.0.1",
				"term-size": "^2.1.0"
			},
			"bin": {
				"changeset": "bin.js"
			}
		},
		"node_modules/@changesets/cli/node_modules/p-limit": {
			"version": "2.3.0",
			"resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz",
			"integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"p-try": "^2.0.0"
			},
			"engines": {
				"node": ">=6"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/@changesets/cli/node_modules/semver": {
			"version": "7.7.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
			"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
			"dev": true,
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/@changesets/config": {
			"version": "3.1.1",
			"resolved": "https://registry.npmjs.org/@changesets/config/-/config-3.1.1.tgz",
			"integrity": "sha512-bd+3Ap2TKXxljCggI0mKPfzCQKeV/TU4yO2h2C6vAihIo8tzseAn2e7klSuiyYYXvgu53zMN1OeYMIQkaQoWnA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/errors": "^0.2.0",
				"@changesets/get-dependents-graph": "^2.1.3",
				"@changesets/logger": "^0.1.1",
				"@changesets/types": "^6.1.0",
				"@manypkg/get-packages": "^1.1.3",
				"fs-extra": "^7.0.1",
				"micromatch": "^4.0.8"
			}
		},
		"node_modules/@changesets/errors": {
			"version": "0.2.0",
			"resolved": "https://registry.npmjs.org/@changesets/errors/-/errors-0.2.0.tgz",
			"integrity": "sha512-6BLOQUscTpZeGljvyQXlWOItQyU71kCdGz7Pi8H8zdw6BI0g3m43iL4xKUVPWtG+qrrL9DTjpdn8eYuCQSRpow==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"extendable-error": "^0.1.5"
			}
		},
		"node_modules/@changesets/get-dependents-graph": {
			"version": "2.1.3",
			"resolved": "https://registry.npmjs.org/@changesets/get-dependents-graph/-/get-dependents-graph-2.1.3.tgz",
			"integrity": "sha512-gphr+v0mv2I3Oxt19VdWRRUxq3sseyUpX9DaHpTUmLj92Y10AGy+XOtV+kbM6L/fDcpx7/ISDFK6T8A/P3lOdQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/types": "^6.1.0",
				"@manypkg/get-packages": "^1.1.3",
				"picocolors": "^1.1.0",
				"semver": "^7.5.3"
			}
		},
		"node_modules/@changesets/get-dependents-graph/node_modules/semver": {
			"version": "7.7.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
			"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
			"dev": true,
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/@changesets/get-github-info": {
			"version": "0.6.0",
			"resolved": "https://registry.npmjs.org/@changesets/get-github-info/-/get-github-info-0.6.0.tgz",
			"integrity": "sha512-v/TSnFVXI8vzX9/w3DU2Ol+UlTZcu3m0kXTjTT4KlAdwSvwutcByYwyYn9hwerPWfPkT2JfpoX0KgvCEi8Q/SA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"dataloader": "^1.4.0",
				"node-fetch": "^2.5.0"
			}
		},
		"node_modules/@changesets/get-github-info/node_modules/node-fetch": {
			"version": "2.7.0",
			"resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz",
			"integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"whatwg-url": "^5.0.0"
			},
			"engines": {
				"node": "4.x || >=6.0.0"
			},
			"peerDependencies": {
				"encoding": "^0.1.0"
			},
			"peerDependenciesMeta": {
				"encoding": {
					"optional": true
				}
			}
		},
		"node_modules/@changesets/get-release-plan": {
			"version": "4.0.8",
			"resolved": "https://registry.npmjs.org/@changesets/get-release-plan/-/get-release-plan-4.0.8.tgz",
			"integrity": "sha512-MM4mq2+DQU1ZT7nqxnpveDMTkMBLnwNX44cX7NSxlXmr7f8hO6/S2MXNiXG54uf/0nYnefv0cfy4Czf/ZL/EKQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/assemble-release-plan": "^6.0.6",
				"@changesets/config": "^3.1.1",
				"@changesets/pre": "^2.0.2",
				"@changesets/read": "^0.6.3",
				"@changesets/types": "^6.1.0",
				"@manypkg/get-packages": "^1.1.3"
			}
		},
		"node_modules/@changesets/get-version-range-type": {
			"version": "0.4.0",
			"resolved": "https://registry.npmjs.org/@changesets/get-version-range-type/-/get-version-range-type-0.4.0.tgz",
			"integrity": "sha512-hwawtob9DryoGTpixy1D3ZXbGgJu1Rhr+ySH2PvTLHvkZuQ7sRT4oQwMh0hbqZH1weAooedEjRsbrWcGLCeyVQ==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@changesets/git": {
			"version": "3.0.2",
			"resolved": "https://registry.npmjs.org/@changesets/git/-/git-3.0.2.tgz",
			"integrity": "sha512-r1/Kju9Y8OxRRdvna+nxpQIsMsRQn9dhhAZt94FLDeu0Hij2hnOozW8iqnHBgvu+KdnJppCveQwK4odwfw/aWQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/errors": "^0.2.0",
				"@manypkg/get-packages": "^1.1.3",
				"is-subdir": "^1.1.1",
				"micromatch": "^4.0.8",
				"spawndamnit": "^3.0.1"
			}
		},
		"node_modules/@changesets/logger": {
			"version": "0.1.1",
			"resolved": "https://registry.npmjs.org/@changesets/logger/-/logger-0.1.1.tgz",
			"integrity": "sha512-OQtR36ZlnuTxKqoW4Sv6x5YIhOmClRd5pWsjZsddYxpWs517R0HkyiefQPIytCVh4ZcC5x9XaG8KTdd5iRQUfg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"picocolors": "^1.1.0"
			}
		},
		"node_modules/@changesets/parse": {
			"version": "0.4.1",
			"resolved": "https://registry.npmjs.org/@changesets/parse/-/parse-0.4.1.tgz",
			"integrity": "sha512-iwksMs5Bf/wUItfcg+OXrEpravm5rEd9Bf4oyIPL4kVTmJQ7PNDSd6MDYkpSJR1pn7tz/k8Zf2DhTCqX08Ou+Q==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/types": "^6.1.0",
				"js-yaml": "^3.13.1"
			}
		},
		"node_modules/@changesets/pre": {
			"version": "2.0.2",
			"resolved": "https://registry.npmjs.org/@changesets/pre/-/pre-2.0.2.tgz",
			"integrity": "sha512-HaL/gEyFVvkf9KFg6484wR9s0qjAXlZ8qWPDkTyKF6+zqjBe/I2mygg3MbpZ++hdi0ToqNUF8cjj7fBy0dg8Ug==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/errors": "^0.2.0",
				"@changesets/types": "^6.1.0",
				"@manypkg/get-packages": "^1.1.3",
				"fs-extra": "^7.0.1"
			}
		},
		"node_modules/@changesets/read": {
			"version": "0.6.3",
			"resolved": "https://registry.npmjs.org/@changesets/read/-/read-0.6.3.tgz",
			"integrity": "sha512-9H4p/OuJ3jXEUTjaVGdQEhBdqoT2cO5Ts95JTFsQyawmKzpL8FnIeJSyhTDPW1MBRDnwZlHFEM9SpPwJDY5wIg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/git": "^3.0.2",
				"@changesets/logger": "^0.1.1",
				"@changesets/parse": "^0.4.1",
				"@changesets/types": "^6.1.0",
				"fs-extra": "^7.0.1",
				"p-filter": "^2.1.0",
				"picocolors": "^1.1.0"
			}
		},
		"node_modules/@changesets/should-skip-package": {
			"version": "0.1.2",
			"resolved": "https://registry.npmjs.org/@changesets/should-skip-package/-/should-skip-package-0.1.2.tgz",
			"integrity": "sha512-qAK/WrqWLNCP22UDdBTMPH5f41elVDlsNyat180A33dWxuUDyNpg6fPi/FyTZwRriVjg0L8gnjJn2F9XAoF0qw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/types": "^6.1.0",
				"@manypkg/get-packages": "^1.1.3"
			}
		},
		"node_modules/@changesets/types": {
			"version": "6.1.0",
			"resolved": "https://registry.npmjs.org/@changesets/types/-/types-6.1.0.tgz",
			"integrity": "sha512-rKQcJ+o1nKNgeoYRHKOS07tAMNd3YSN0uHaJOZYjBAgxfV7TUE7JE+z4BzZdQwb5hKaYbayKN5KrYV7ODb2rAA==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@changesets/write": {
			"version": "0.4.0",
			"resolved": "https://registry.npmjs.org/@changesets/write/-/write-0.4.0.tgz",
			"integrity": "sha512-CdTLvIOPiCNuH71pyDu3rA+Q0n65cmAbXnwWH84rKGiFumFzkmHNT8KHTMEchcxN+Kl8I54xGUhJ7l3E7X396Q==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@changesets/types": "^6.1.0",
				"fs-extra": "^7.0.1",
				"human-id": "^4.1.1",
				"prettier": "^2.7.1"
			}
		},
		"node_modules/@changesets/write/node_modules/prettier": {
			"version": "2.8.8",
			"resolved": "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz",
			"integrity": "sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==",
			"dev": true,
			"license": "MIT",
			"bin": {
				"prettier": "bin-prettier.js"
			},
			"engines": {
				"node": ">=10.13.0"
			},
			"funding": {
				"url": "https://github.com/prettier/prettier?sponsor=1"
			}
		},
		"node_modules/@colors/colors": {
			"version": "1.5.0",
			"resolved": "https://registry.npmjs.org/@colors/colors/-/colors-1.5.0.tgz",
			"integrity": "sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==",
			"license": "MIT",
			"optional": true,
			"engines": {
				"node": ">=0.1.90"
			}
		},
		"node_modules/@esbuild/aix-ppc64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.3.tgz",
			"integrity": "sha512-W8bFfPA8DowP8l//sxjJLSLkD8iEjMc7cBVyP+u4cEv9sM7mdUCkgsj+t0n/BWPFtv7WWCN5Yzj0N6FJNUUqBQ==",
			"cpu": [
				"ppc64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"aix"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/android-arm": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.3.tgz",
			"integrity": "sha512-PuwVXbnP87Tcff5I9ngV0lmiSu40xw1At6i3GsU77U7cjDDB4s0X2cyFuBiDa1SBk9DnvWwnGvVaGBqoFWPb7A==",
			"cpu": [
				"arm"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"android"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/android-arm64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.3.tgz",
			"integrity": "sha512-XelR6MzjlZuBM4f5z2IQHK6LkK34Cvv6Rj2EntER3lwCBFdg6h2lKbtRjpTTsdEjD/WSe1q8UyPBXP1x3i/wYQ==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"android"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/android-x64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.3.tgz",
			"integrity": "sha512-ogtTpYHT/g1GWS/zKM0cc/tIebFjm1F9Aw1boQ2Y0eUQ+J89d0jFY//s9ei9jVIlkYi8AfOjiixcLJSGNSOAdQ==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"android"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/darwin-arm64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.3.tgz",
			"integrity": "sha512-eESK5yfPNTqpAmDfFWNsOhmIOaQA59tAcF/EfYvo5/QWQCzXn5iUSOnqt3ra3UdzBv073ykTtmeLJZGt3HhA+w==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"darwin"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/darwin-x64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.3.tgz",
			"integrity": "sha512-Kd8glo7sIZtwOLcPbW0yLpKmBNWMANZhrC1r6K++uDR2zyzb6AeOYtI6udbtabmQpFaxJ8uduXMAo1gs5ozz8A==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"darwin"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/freebsd-arm64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.3.tgz",
			"integrity": "sha512-EJiyS70BYybOBpJth3M0KLOus0n+RRMKTYzhYhFeMwp7e/RaajXvP+BWlmEXNk6uk+KAu46j/kaQzr6au+JcIw==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"freebsd"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/freebsd-x64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.3.tgz",
			"integrity": "sha512-Q+wSjaLpGxYf7zC0kL0nDlhsfuFkoN+EXrx2KSB33RhinWzejOd6AvgmP5JbkgXKmjhmpfgKZq24pneodYqE8Q==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"freebsd"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-arm": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.3.tgz",
			"integrity": "sha512-dUOVmAUzuHy2ZOKIHIKHCm58HKzFqd+puLaS424h6I85GlSDRZIA5ycBixb3mFgM0Jdh+ZOSB6KptX30DD8YOQ==",
			"cpu": [
				"arm"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-arm64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.3.tgz",
			"integrity": "sha512-xCUgnNYhRD5bb1C1nqrDV1PfkwgbswTTBRbAd8aH5PhYzikdf/ddtsYyMXFfGSsb/6t6QaPSzxtbfAZr9uox4A==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-ia32": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.3.tgz",
			"integrity": "sha512-yplPOpczHOO4jTYKmuYuANI3WhvIPSVANGcNUeMlxH4twz/TeXuzEP41tGKNGWJjuMhotpGabeFYGAOU2ummBw==",
			"cpu": [
				"ia32"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-loong64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.3.tgz",
			"integrity": "sha512-P4BLP5/fjyihmXCELRGrLd793q/lBtKMQl8ARGpDxgzgIKJDRJ/u4r1A/HgpBpKpKZelGct2PGI4T+axcedf6g==",
			"cpu": [
				"loong64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-mips64el": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.3.tgz",
			"integrity": "sha512-eRAOV2ODpu6P5divMEMa26RRqb2yUoYsuQQOuFUexUoQndm4MdpXXDBbUoKIc0iPa4aCO7gIhtnYomkn2x+bag==",
			"cpu": [
				"mips64el"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-ppc64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.3.tgz",
			"integrity": "sha512-ZC4jV2p7VbzTlnl8nZKLcBkfzIf4Yad1SJM4ZMKYnJqZFD4rTI+pBG65u8ev4jk3/MPwY9DvGn50wi3uhdaghg==",
			"cpu": [
				"ppc64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-riscv64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.3.tgz",
			"integrity": "sha512-LDDODcFzNtECTrUUbVCs6j9/bDVqy7DDRsuIXJg6so+mFksgwG7ZVnTruYi5V+z3eE5y+BJZw7VvUadkbfg7QA==",
			"cpu": [
				"riscv64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-s390x": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.3.tgz",
			"integrity": "sha512-s+w/NOY2k0yC2p9SLen+ymflgcpRkvwwa02fqmAwhBRI3SC12uiS10edHHXlVWwfAagYSY5UpmT/zISXPMW3tQ==",
			"cpu": [
				"s390x"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/linux-x64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.3.tgz",
			"integrity": "sha512-nQHDz4pXjSDC6UfOE1Fw9Q8d6GCAd9KdvMZpfVGWSJztYCarRgSDfOVBY5xwhQXseiyxapkiSJi/5/ja8mRFFA==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"linux"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/netbsd-arm64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.3.tgz",
			"integrity": "sha512-1QaLtOWq0mzK6tzzp0jRN3eccmN3hezey7mhLnzC6oNlJoUJz4nym5ZD7mDnS/LZQgkrhEbEiTn515lPeLpgWA==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"netbsd"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/netbsd-x64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.3.tgz",
			"integrity": "sha512-i5Hm68HXHdgv8wkrt+10Bc50zM0/eonPb/a/OFVfB6Qvpiirco5gBA5bz7S2SHuU+Y4LWn/zehzNX14Sp4r27g==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"netbsd"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/openbsd-arm64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.3.tgz",
			"integrity": "sha512-zGAVApJEYTbOC6H/3QBr2mq3upG/LBEXr85/pTtKiv2IXcgKV0RT0QA/hSXZqSvLEpXeIxah7LczB4lkiYhTAQ==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"openbsd"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/openbsd-x64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.3.tgz",
			"integrity": "sha512-fpqctI45NnCIDKBH5AXQBsD0NDPbEFczK98hk/aa6HJxbl+UtLkJV2+Bvy5hLSLk3LHmqt0NTkKNso1A9y1a4w==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"openbsd"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/sunos-x64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.3.tgz",
			"integrity": "sha512-ROJhm7d8bk9dMCUZjkS8fgzsPAZEjtRJqCAmVgB0gMrvG7hfmPmz9k1rwO4jSiblFjYmNvbECL9uhaPzONMfgA==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"sunos"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/win32-arm64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.3.tgz",
			"integrity": "sha512-YWcow8peiHpNBiIXHwaswPnAXLsLVygFwCB3A7Bh5jRkIBFWHGmNQ48AlX4xDvQNoMZlPYzjVOQDYEzWCqufMQ==",
			"cpu": [
				"arm64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"win32"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/win32-ia32": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.3.tgz",
			"integrity": "sha512-qspTZOIGoXVS4DpNqUYUs9UxVb04khS1Degaw/MnfMe7goQ3lTfQ13Vw4qY/Nj0979BGvMRpAYbs/BAxEvU8ew==",
			"cpu": [
				"ia32"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"win32"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@esbuild/win32-x64": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.3.tgz",
			"integrity": "sha512-ICgUR+kPimx0vvRzf+N/7L7tVSQeE3BYY+NhHRHXS1kBuPO7z2+7ea2HbhDyZdTephgvNvKrlDDKUexuCVBVvg==",
			"cpu": [
				"x64"
			],
			"dev": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"win32"
			],
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@inquirer/checkbox": {
			"version": "4.1.4",
			"resolved": "https://registry.npmjs.org/@inquirer/checkbox/-/checkbox-4.1.4.tgz",
			"integrity": "sha512-d30576EZdApjAMceijXA5jDzRQHT/MygbC+J8I7EqA6f/FRpYxlRtRJbHF8gHeWYeSdOuTEJqonn7QLB1ELezA==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/figures": "^1.0.11",
				"@inquirer/type": "^3.0.5",
				"ansi-escapes": "^4.3.2",
				"yoctocolors-cjs": "^2.1.2"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/confirm": {
			"version": "5.1.8",
			"resolved": "https://registry.npmjs.org/@inquirer/confirm/-/confirm-5.1.8.tgz",
			"integrity": "sha512-dNLWCYZvXDjO3rnQfk2iuJNL4Ivwz/T2+C3+WnNfJKsNGSuOs3wAo2F6e0p946gtSAk31nZMfW+MRmYaplPKsg==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/type": "^3.0.5"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/core": {
			"version": "10.1.9",
			"resolved": "https://registry.npmjs.org/@inquirer/core/-/core-10.1.9.tgz",
			"integrity": "sha512-sXhVB8n20NYkUBfDYgizGHlpRVaCRjtuzNZA6xpALIUbkgfd2Hjz+DfEN6+h1BRnuxw0/P4jCIMjMsEOAMwAJw==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/figures": "^1.0.11",
				"@inquirer/type": "^3.0.5",
				"ansi-escapes": "^4.3.2",
				"cli-width": "^4.1.0",
				"mute-stream": "^2.0.0",
				"signal-exit": "^4.1.0",
				"wrap-ansi": "^6.2.0",
				"yoctocolors-cjs": "^2.1.2"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/core/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@inquirer/core/node_modules/emoji-regex": {
			"version": "8.0.0",
			"resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
			"integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
			"license": "MIT"
		},
		"node_modules/@inquirer/core/node_modules/string-width": {
			"version": "4.2.3",
			"resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
			"integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
			"license": "MIT",
			"dependencies": {
				"emoji-regex": "^8.0.0",
				"is-fullwidth-code-point": "^3.0.0",
				"strip-ansi": "^6.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@inquirer/core/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@inquirer/core/node_modules/wrap-ansi": {
			"version": "6.2.0",
			"resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz",
			"integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==",
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.0.0",
				"string-width": "^4.1.0",
				"strip-ansi": "^6.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@inquirer/editor": {
			"version": "4.2.9",
			"resolved": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.9.tgz",
			"integrity": "sha512-8HjOppAxO7O4wV1ETUlJFg6NDjp/W2NP5FB9ZPAcinAlNT4ZIWOLe2pUVwmmPRSV0NMdI5r/+lflN55AwZOKSw==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/type": "^3.0.5",
				"external-editor": "^3.1.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/expand": {
			"version": "4.0.11",
			"resolved": "https://registry.npmjs.org/@inquirer/expand/-/expand-4.0.11.tgz",
			"integrity": "sha512-OZSUW4hFMW2TYvX/Sv+NnOZgO8CHT2TU1roUCUIF2T+wfw60XFRRp9MRUPCT06cRnKL+aemt2YmTWwt7rOrNEA==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/type": "^3.0.5",
				"yoctocolors-cjs": "^2.1.2"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/figures": {
			"version": "1.0.11",
			"resolved": "https://registry.npmjs.org/@inquirer/figures/-/figures-1.0.11.tgz",
			"integrity": "sha512-eOg92lvrn/aRUqbxRyvpEWnrvRuTYRifixHkYVpJiygTgVSBIHDqLh0SrMQXkafvULg3ck11V7xvR+zcgvpHFw==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@inquirer/input": {
			"version": "4.1.8",
			"resolved": "https://registry.npmjs.org/@inquirer/input/-/input-4.1.8.tgz",
			"integrity": "sha512-WXJI16oOZ3/LiENCAxe8joniNp8MQxF6Wi5V+EBbVA0ZIOpFcL4I9e7f7cXse0HJeIPCWO8Lcgnk98juItCi7Q==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/type": "^3.0.5"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/number": {
			"version": "3.0.11",
			"resolved": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.11.tgz",
			"integrity": "sha512-pQK68CsKOgwvU2eA53AG/4npRTH2pvs/pZ2bFvzpBhrznh8Mcwt19c+nMO7LHRr3Vreu1KPhNBF3vQAKrjIulw==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/type": "^3.0.5"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/password": {
			"version": "4.0.11",
			"resolved": "https://registry.npmjs.org/@inquirer/password/-/password-4.0.11.tgz",
			"integrity": "sha512-dH6zLdv+HEv1nBs96Case6eppkRggMe8LoOTl30+Gq5Wf27AO/vHFgStTVz4aoevLdNXqwE23++IXGw4eiOXTg==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/type": "^3.0.5",
				"ansi-escapes": "^4.3.2"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/prompts": {
			"version": "7.4.0",
			"resolved": "https://registry.npmjs.org/@inquirer/prompts/-/prompts-7.4.0.tgz",
			"integrity": "sha512-EZiJidQOT4O5PYtqnu1JbF0clv36oW2CviR66c7ma4LsupmmQlUwmdReGKRp456OWPWMz3PdrPiYg3aCk3op2w==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/checkbox": "^4.1.4",
				"@inquirer/confirm": "^5.1.8",
				"@inquirer/editor": "^4.2.9",
				"@inquirer/expand": "^4.0.11",
				"@inquirer/input": "^4.1.8",
				"@inquirer/number": "^3.0.11",
				"@inquirer/password": "^4.0.11",
				"@inquirer/rawlist": "^4.0.11",
				"@inquirer/search": "^3.0.11",
				"@inquirer/select": "^4.1.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/rawlist": {
			"version": "4.0.11",
			"resolved": "https://registry.npmjs.org/@inquirer/rawlist/-/rawlist-4.0.11.tgz",
			"integrity": "sha512-uAYtTx0IF/PqUAvsRrF3xvnxJV516wmR6YVONOmCWJbbt87HcDHLfL9wmBQFbNJRv5kCjdYKrZcavDkH3sVJPg==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/type": "^3.0.5",
				"yoctocolors-cjs": "^2.1.2"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/search": {
			"version": "3.0.11",
			"resolved": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.11.tgz",
			"integrity": "sha512-9CWQT0ikYcg6Ls3TOa7jljsD7PgjcsYEM0bYE+Gkz+uoW9u8eaJCRHJKkucpRE5+xKtaaDbrND+nPDoxzjYyew==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/figures": "^1.0.11",
				"@inquirer/type": "^3.0.5",
				"yoctocolors-cjs": "^2.1.2"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/select": {
			"version": "4.1.0",
			"resolved": "https://registry.npmjs.org/@inquirer/select/-/select-4.1.0.tgz",
			"integrity": "sha512-z0a2fmgTSRN+YBuiK1ROfJ2Nvrpij5lVN3gPDkQGhavdvIVGHGW29LwYZfM/j42Ai2hUghTI/uoBuTbrJk42bA==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/figures": "^1.0.11",
				"@inquirer/type": "^3.0.5",
				"ansi-escapes": "^4.3.2",
				"yoctocolors-cjs": "^2.1.2"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@inquirer/type": {
			"version": "3.0.5",
			"resolved": "https://registry.npmjs.org/@inquirer/type/-/type-3.0.5.tgz",
			"integrity": "sha512-ZJpeIYYueOz/i/ONzrfof8g89kNdO2hjGuvULROo3O8rlB2CRtSseE5KeirnyE4t/thAn/EwvS/vuQeJCn+NZg==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/@istanbuljs/load-nyc-config": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz",
			"integrity": "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"camelcase": "^5.3.1",
				"find-up": "^4.1.0",
				"get-package-type": "^0.1.0",
				"js-yaml": "^3.13.1",
				"resolve-from": "^5.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase": {
			"version": "5.3.1",
			"resolved": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz",
			"integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/@istanbuljs/schema": {
			"version": "0.1.3",
			"resolved": "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz",
			"integrity": "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@jest/console": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz",
			"integrity": "sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"chalk": "^4.0.0",
				"jest-message-util": "^29.7.0",
				"jest-util": "^29.7.0",
				"slash": "^3.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/console/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/@jest/core": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/core/-/core-29.7.0.tgz",
			"integrity": "sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/console": "^29.7.0",
				"@jest/reporters": "^29.7.0",
				"@jest/test-result": "^29.7.0",
				"@jest/transform": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"ansi-escapes": "^4.2.1",
				"chalk": "^4.0.0",
				"ci-info": "^3.2.0",
				"exit": "^0.1.2",
				"graceful-fs": "^4.2.9",
				"jest-changed-files": "^29.7.0",
				"jest-config": "^29.7.0",
				"jest-haste-map": "^29.7.0",
				"jest-message-util": "^29.7.0",
				"jest-regex-util": "^29.6.3",
				"jest-resolve": "^29.7.0",
				"jest-resolve-dependencies": "^29.7.0",
				"jest-runner": "^29.7.0",
				"jest-runtime": "^29.7.0",
				"jest-snapshot": "^29.7.0",
				"jest-util": "^29.7.0",
				"jest-validate": "^29.7.0",
				"jest-watcher": "^29.7.0",
				"micromatch": "^4.0.4",
				"pretty-format": "^29.7.0",
				"slash": "^3.0.0",
				"strip-ansi": "^6.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			},
			"peerDependencies": {
				"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"
			},
			"peerDependenciesMeta": {
				"node-notifier": {
					"optional": true
				}
			}
		},
		"node_modules/@jest/core/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@jest/core/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/@jest/core/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@jest/environment": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz",
			"integrity": "sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/fake-timers": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"jest-mock": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/expect": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz",
			"integrity": "sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"expect": "^29.7.0",
				"jest-snapshot": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/expect-utils": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz",
			"integrity": "sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"jest-get-type": "^29.6.3"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/fake-timers": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz",
			"integrity": "sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/types": "^29.6.3",
				"@sinonjs/fake-timers": "^10.0.2",
				"@types/node": "*",
				"jest-message-util": "^29.7.0",
				"jest-mock": "^29.7.0",
				"jest-util": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/globals": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz",
			"integrity": "sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/environment": "^29.7.0",
				"@jest/expect": "^29.7.0",
				"@jest/types": "^29.6.3",
				"jest-mock": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/reporters": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/reporters/-/reporters-29.7.0.tgz",
			"integrity": "sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@bcoe/v8-coverage": "^0.2.3",
				"@jest/console": "^29.7.0",
				"@jest/test-result": "^29.7.0",
				"@jest/transform": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@jridgewell/trace-mapping": "^0.3.18",
				"@types/node": "*",
				"chalk": "^4.0.0",
				"collect-v8-coverage": "^1.0.0",
				"exit": "^0.1.2",
				"glob": "^7.1.3",
				"graceful-fs": "^4.2.9",
				"istanbul-lib-coverage": "^3.0.0",
				"istanbul-lib-instrument": "^6.0.0",
				"istanbul-lib-report": "^3.0.0",
				"istanbul-lib-source-maps": "^4.0.0",
				"istanbul-reports": "^3.1.3",
				"jest-message-util": "^29.7.0",
				"jest-util": "^29.7.0",
				"jest-worker": "^29.7.0",
				"slash": "^3.0.0",
				"string-length": "^4.0.1",
				"strip-ansi": "^6.0.0",
				"v8-to-istanbul": "^9.0.1"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			},
			"peerDependencies": {
				"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"
			},
			"peerDependenciesMeta": {
				"node-notifier": {
					"optional": true
				}
			}
		},
		"node_modules/@jest/reporters/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@jest/reporters/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/@jest/reporters/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/@jest/schemas": {
			"version": "29.6.3",
			"resolved": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz",
			"integrity": "sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@sinclair/typebox": "^0.27.8"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/source-map": {
			"version": "29.6.3",
			"resolved": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz",
			"integrity": "sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jridgewell/trace-mapping": "^0.3.18",
				"callsites": "^3.0.0",
				"graceful-fs": "^4.2.9"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/test-result": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz",
			"integrity": "sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/console": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@types/istanbul-lib-coverage": "^2.0.0",
				"collect-v8-coverage": "^1.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/test-sequencer": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz",
			"integrity": "sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/test-result": "^29.7.0",
				"graceful-fs": "^4.2.9",
				"jest-haste-map": "^29.7.0",
				"slash": "^3.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/transform": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz",
			"integrity": "sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/core": "^7.11.6",
				"@jest/types": "^29.6.3",
				"@jridgewell/trace-mapping": "^0.3.18",
				"babel-plugin-istanbul": "^6.1.1",
				"chalk": "^4.0.0",
				"convert-source-map": "^2.0.0",
				"fast-json-stable-stringify": "^2.1.0",
				"graceful-fs": "^4.2.9",
				"jest-haste-map": "^29.7.0",
				"jest-regex-util": "^29.6.3",
				"jest-util": "^29.7.0",
				"micromatch": "^4.0.4",
				"pirates": "^4.0.4",
				"slash": "^3.0.0",
				"write-file-atomic": "^4.0.2"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/transform/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/@jest/types": {
			"version": "29.6.3",
			"resolved": "https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz",
			"integrity": "sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/schemas": "^29.6.3",
				"@types/istanbul-lib-coverage": "^2.0.0",
				"@types/istanbul-reports": "^3.0.0",
				"@types/node": "*",
				"@types/yargs": "^17.0.8",
				"chalk": "^4.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/@jest/types/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/@jridgewell/gen-mapping": {
			"version": "0.3.8",
			"resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz",
			"integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jridgewell/set-array": "^1.2.1",
				"@jridgewell/sourcemap-codec": "^1.4.10",
				"@jridgewell/trace-mapping": "^0.3.24"
			},
			"engines": {
				"node": ">=6.0.0"
			}
		},
		"node_modules/@jridgewell/resolve-uri": {
			"version": "3.1.2",
			"resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz",
			"integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6.0.0"
			}
		},
		"node_modules/@jridgewell/set-array": {
			"version": "1.2.1",
			"resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz",
			"integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6.0.0"
			}
		},
		"node_modules/@jridgewell/sourcemap-codec": {
			"version": "1.5.0",
			"resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz",
			"integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@jridgewell/trace-mapping": {
			"version": "0.3.25",
			"resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz",
			"integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jridgewell/resolve-uri": "^3.1.0",
				"@jridgewell/sourcemap-codec": "^1.4.14"
			}
		},
		"node_modules/@manypkg/find-root": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/@manypkg/find-root/-/find-root-1.1.0.tgz",
			"integrity": "sha512-mki5uBvhHzO8kYYix/WRy2WX8S3B5wdVSc9D6KcU5lQNglP2yt58/VfLuAK49glRXChosY8ap2oJ1qgma3GUVA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/runtime": "^7.5.5",
				"@types/node": "^12.7.1",
				"find-up": "^4.1.0",
				"fs-extra": "^8.1.0"
			}
		},
		"node_modules/@manypkg/find-root/node_modules/@types/node": {
			"version": "12.20.55",
			"resolved": "https://registry.npmjs.org/@types/node/-/node-12.20.55.tgz",
			"integrity": "sha512-J8xLz7q2OFulZ2cyGTLE1TbbZcjpno7FaN6zdJNrgAdrJ+DZzh/uFR6YrTb4C+nXakvud8Q4+rbhoIWlYQbUFQ==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@manypkg/find-root/node_modules/fs-extra": {
			"version": "8.1.0",
			"resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz",
			"integrity": "sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"graceful-fs": "^4.2.0",
				"jsonfile": "^4.0.0",
				"universalify": "^0.1.0"
			},
			"engines": {
				"node": ">=6 <7 || >=8"
			}
		},
		"node_modules/@manypkg/get-packages": {
			"version": "1.1.3",
			"resolved": "https://registry.npmjs.org/@manypkg/get-packages/-/get-packages-1.1.3.tgz",
			"integrity": "sha512-fo+QhuU3qE/2TQMQmbVMqaQ6EWbMhi4ABWP+O4AM1NqPBuy0OrApV5LO6BrrgnhtAHS2NH6RrVk9OL181tTi8A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/runtime": "^7.5.5",
				"@changesets/types": "^4.0.1",
				"@manypkg/find-root": "^1.1.0",
				"fs-extra": "^8.1.0",
				"globby": "^11.0.0",
				"read-yaml-file": "^1.1.0"
			}
		},
		"node_modules/@manypkg/get-packages/node_modules/@changesets/types": {
			"version": "4.1.0",
			"resolved": "https://registry.npmjs.org/@changesets/types/-/types-4.1.0.tgz",
			"integrity": "sha512-LDQvVDv5Kb50ny2s25Fhm3d9QSZimsoUGBsUioj6MC3qbMUCuC8GPIvk/M6IvXx3lYhAs0lwWUQLb+VIEUCECw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@manypkg/get-packages/node_modules/fs-extra": {
			"version": "8.1.0",
			"resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz",
			"integrity": "sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"graceful-fs": "^4.2.0",
				"jsonfile": "^4.0.0",
				"universalify": "^0.1.0"
			},
			"engines": {
				"node": ">=6 <7 || >=8"
			}
		},
		"node_modules/@modelcontextprotocol/sdk": {
			"version": "1.12.1",
			"resolved": "https://registry.npmjs.org/@modelcontextprotocol/sdk/-/sdk-1.12.1.tgz",
			"integrity": "sha512-KG1CZhZfWg+u8pxeM/mByJDScJSrjjxLc8fwQqbsS8xCjBmQfMNEBTotYdNanKekepnfRI85GtgQlctLFpcYPw==",
			"license": "MIT",
			"dependencies": {
				"ajv": "^6.12.6",
				"content-type": "^1.0.5",
				"cors": "^2.8.5",
				"cross-spawn": "^7.0.5",
				"eventsource": "^3.0.2",
				"express": "^5.0.1",
				"express-rate-limit": "^7.5.0",
				"pkce-challenge": "^5.0.0",
				"raw-body": "^3.0.0",
				"zod": "^3.23.8",
				"zod-to-json-schema": "^3.24.1"
			},
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/accepts": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz",
			"integrity": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==",
			"license": "MIT",
			"dependencies": {
				"mime-types": "^3.0.0",
				"negotiator": "^1.0.0"
			},
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/body-parser": {
			"version": "2.2.0",
			"resolved": "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz",
			"integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==",
			"license": "MIT",
			"dependencies": {
				"bytes": "^3.1.2",
				"content-type": "^1.0.5",
				"debug": "^4.4.0",
				"http-errors": "^2.0.0",
				"iconv-lite": "^0.6.3",
				"on-finished": "^2.4.1",
				"qs": "^6.14.0",
				"raw-body": "^3.0.0",
				"type-is": "^2.0.0"
			},
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/content-disposition": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz",
			"integrity": "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==",
			"license": "MIT",
			"dependencies": {
				"safe-buffer": "5.2.1"
			},
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/cookie-signature": {
			"version": "1.2.2",
			"resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz",
			"integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==",
			"license": "MIT",
			"engines": {
				"node": ">=6.6.0"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/express": {
			"version": "5.1.0",
			"resolved": "https://registry.npmjs.org/express/-/express-5.1.0.tgz",
			"integrity": "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==",
			"license": "MIT",
			"dependencies": {
				"accepts": "^2.0.0",
				"body-parser": "^2.2.0",
				"content-disposition": "^1.0.0",
				"content-type": "^1.0.5",
				"cookie": "^0.7.1",
				"cookie-signature": "^1.2.1",
				"debug": "^4.4.0",
				"encodeurl": "^2.0.0",
				"escape-html": "^1.0.3",
				"etag": "^1.8.1",
				"finalhandler": "^2.1.0",
				"fresh": "^2.0.0",
				"http-errors": "^2.0.0",
				"merge-descriptors": "^2.0.0",
				"mime-types": "^3.0.0",
				"on-finished": "^2.4.1",
				"once": "^1.4.0",
				"parseurl": "^1.3.3",
				"proxy-addr": "^2.0.7",
				"qs": "^6.14.0",
				"range-parser": "^1.2.1",
				"router": "^2.2.0",
				"send": "^1.1.0",
				"serve-static": "^2.2.0",
				"statuses": "^2.0.1",
				"type-is": "^2.0.1",
				"vary": "^1.1.2"
			},
			"engines": {
				"node": ">= 18"
			},
			"funding": {
				"type": "opencollective",
				"url": "https://opencollective.com/express"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/finalhandler": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz",
			"integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==",
			"license": "MIT",
			"dependencies": {
				"debug": "^4.4.0",
				"encodeurl": "^2.0.0",
				"escape-html": "^1.0.3",
				"on-finished": "^2.4.1",
				"parseurl": "^1.3.3",
				"statuses": "^2.0.1"
			},
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/fresh": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz",
			"integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite": {
			"version": "0.6.3",
			"resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz",
			"integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==",
			"license": "MIT",
			"dependencies": {
				"safer-buffer": ">= 2.1.2 < 3.0.0"
			},
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/media-typer": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz",
			"integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/merge-descriptors": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz",
			"integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/mime-db": {
			"version": "1.54.0",
			"resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz",
			"integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/mime-types": {
			"version": "3.0.1",
			"resolved": "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz",
			"integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==",
			"license": "MIT",
			"dependencies": {
				"mime-db": "^1.54.0"
			},
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/negotiator": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz",
			"integrity": "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/raw-body": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz",
			"integrity": "sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==",
			"license": "MIT",
			"dependencies": {
				"bytes": "3.1.2",
				"http-errors": "2.0.0",
				"iconv-lite": "0.6.3",
				"unpipe": "1.0.0"
			},
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/send": {
			"version": "1.2.0",
			"resolved": "https://registry.npmjs.org/send/-/send-1.2.0.tgz",
			"integrity": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==",
			"license": "MIT",
			"dependencies": {
				"debug": "^4.3.5",
				"encodeurl": "^2.0.0",
				"escape-html": "^1.0.3",
				"etag": "^1.8.1",
				"fresh": "^2.0.0",
				"http-errors": "^2.0.0",
				"mime-types": "^3.0.1",
				"ms": "^2.1.3",
				"on-finished": "^2.4.1",
				"range-parser": "^1.2.1",
				"statuses": "^2.0.1"
			},
			"engines": {
				"node": ">= 18"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/serve-static": {
			"version": "2.2.0",
			"resolved": "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz",
			"integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==",
			"license": "MIT",
			"dependencies": {
				"encodeurl": "^2.0.0",
				"escape-html": "^1.0.3",
				"parseurl": "^1.3.3",
				"send": "^1.2.0"
			},
			"engines": {
				"node": ">= 18"
			}
		},
		"node_modules/@modelcontextprotocol/sdk/node_modules/type-is": {
			"version": "2.0.1",
			"resolved": "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz",
			"integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==",
			"license": "MIT",
			"dependencies": {
				"content-type": "^1.0.5",
				"media-typer": "^1.1.0",
				"mime-types": "^3.0.0"
			},
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/@nodelib/fs.scandir": {
			"version": "2.1.5",
			"resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz",
			"integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@nodelib/fs.stat": "2.0.5",
				"run-parallel": "^1.1.9"
			},
			"engines": {
				"node": ">= 8"
			}
		},
		"node_modules/@nodelib/fs.stat": {
			"version": "2.0.5",
			"resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz",
			"integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">= 8"
			}
		},
		"node_modules/@nodelib/fs.walk": {
			"version": "1.2.8",
			"resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz",
			"integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@nodelib/fs.scandir": "2.1.5",
				"fastq": "^1.6.0"
			},
			"engines": {
				"node": ">= 8"
			}
		},
		"node_modules/@openrouter/ai-sdk-provider": {
			"version": "0.4.5",
			"resolved": "https://registry.npmjs.org/@openrouter/ai-sdk-provider/-/ai-sdk-provider-0.4.5.tgz",
			"integrity": "sha512-gbCOcSjNhyWlLHyYZX2rIFnpJi3C2RXNyyzJj+d6pMRfTS/mdvEEOsU66KxK9H8Qju2i9YRLOn/FdQT26K7bIQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.0.9",
				"@ai-sdk/provider-utils": "2.1.10"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			}
		},
		"node_modules/@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider": {
			"version": "1.0.9",
			"resolved": "https://registry.npmjs.org/@ai-sdk/provider/-/provider-1.0.9.tgz",
			"integrity": "sha512-jie6ZJT2ZR0uVOVCDc9R2xCX5I/Dum/wEK28lx21PJx6ZnFAN9EzD2WsPhcDWfCgGx3OAZZ0GyM3CEobXpa9LA==",
			"license": "Apache-2.0",
			"dependencies": {
				"json-schema": "^0.4.0"
			},
			"engines": {
				"node": ">=18"
			}
		},
		"node_modules/@openrouter/ai-sdk-provider/node_modules/@ai-sdk/provider-utils": {
			"version": "2.1.10",
			"resolved": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.10.tgz",
			"integrity": "sha512-4GZ8GHjOFxePFzkl3q42AU0DQOtTQ5w09vmaWUf/pKFXJPizlnzKSUkF0f+VkapIUfDugyMqPMT1ge8XQzVI7Q==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.0.9",
				"eventsource-parser": "^3.0.0",
				"nanoid": "^3.3.8",
				"secure-json-parse": "^2.7.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			},
			"peerDependenciesMeta": {
				"zod": {
					"optional": true
				}
			}
		},
		"node_modules/@opentelemetry/api": {
			"version": "1.9.0",
			"resolved": "https://registry.npmjs.org/@opentelemetry/api/-/api-1.9.0.tgz",
			"integrity": "sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==",
			"license": "Apache-2.0",
			"engines": {
				"node": ">=8.0.0"
			}
		},
		"node_modules/@sec-ant/readable-stream": {
			"version": "0.4.1",
			"resolved": "https://registry.npmjs.org/@sec-ant/readable-stream/-/readable-stream-0.4.1.tgz",
			"integrity": "sha512-831qok9r2t8AlxLko40y2ebgSDhenenCatLVeW/uBtnHPyhHOvG0C7TvfgecV+wHzIm5KUICgzmVpWS+IMEAeg==",
			"license": "MIT"
		},
		"node_modules/@sinclair/typebox": {
			"version": "0.27.8",
			"resolved": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz",
			"integrity": "sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@sindresorhus/merge-streams": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@sindresorhus/merge-streams/-/merge-streams-4.0.0.tgz",
			"integrity": "sha512-tlqY9xq5ukxTUZBmoOp+m61cqwQD5pHJtFY3Mn8CA8ps6yghLH/Hw8UPdqg4OLmFW3IFlcXnQNmo/dh8HzXYIQ==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/@sinonjs/commons": {
			"version": "3.0.1",
			"resolved": "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz",
			"integrity": "sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"type-detect": "4.0.8"
			}
		},
		"node_modules/@sinonjs/fake-timers": {
			"version": "10.3.0",
			"resolved": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz",
			"integrity": "sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"@sinonjs/commons": "^3.0.0"
			}
		},
		"node_modules/@smithy/abort-controller": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-4.0.3.tgz",
			"integrity": "sha512-AqXFf6DXnuRBXy4SoK/n1mfgHaKaq36bmkphmD1KO0nHq6xK/g9KHSW4HEsPQUBCGdIEfuJifGHwxFXPIFay9Q==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/config-resolver": {
			"version": "4.1.3",
			"resolved": "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-4.1.3.tgz",
			"integrity": "sha512-N5e7ofiyYDmHxnPnqF8L4KtsbSDwyxFRfDK9bp1d9OyPO4ytRLd0/XxCqi5xVaaqB65v4woW8uey6jND6zxzxQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/node-config-provider": "^4.1.2",
				"@smithy/types": "^4.3.0",
				"@smithy/util-config-provider": "^4.0.0",
				"@smithy/util-middleware": "^4.0.3",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/core": {
			"version": "3.4.0",
			"resolved": "https://registry.npmjs.org/@smithy/core/-/core-3.4.0.tgz",
			"integrity": "sha512-dDYISQo7k0Ml/rXlFIjkTmTcQze/LxhtIRAEmZ6HJ/EI0inVxVEVnrUXJ7jPx6ZP0GHUhFm40iQcCgS5apXIXA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/middleware-serde": "^4.0.6",
				"@smithy/protocol-http": "^5.1.1",
				"@smithy/types": "^4.3.0",
				"@smithy/util-body-length-browser": "^4.0.0",
				"@smithy/util-middleware": "^4.0.3",
				"@smithy/util-stream": "^4.2.1",
				"@smithy/util-utf8": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/credential-provider-imds": {
			"version": "4.0.5",
			"resolved": "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-4.0.5.tgz",
			"integrity": "sha512-saEAGwrIlkb9XxX/m5S5hOtzjoJPEK6Qw2f9pYTbIsMPOFyGSXBBTw95WbOyru8A1vIS2jVCCU1Qhz50QWG3IA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/node-config-provider": "^4.1.2",
				"@smithy/property-provider": "^4.0.3",
				"@smithy/types": "^4.3.0",
				"@smithy/url-parser": "^4.0.3",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/eventstream-codec": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-4.0.3.tgz",
			"integrity": "sha512-V22KIPXZsE2mc4zEgYGANM/7UbL9jWlOACEolyGyMuTY+jjHJ2PQ0FdopOTS1CS7u6PlAkALmypkv2oQ4aftcg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@aws-crypto/crc32": "5.2.0",
				"@smithy/types": "^4.3.0",
				"@smithy/util-hex-encoding": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/fetch-http-handler": {
			"version": "5.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.0.3.tgz",
			"integrity": "sha512-yBZwavI31roqTndNI7ONHqesfH01JmjJK6L3uUpZAhyAmr86LN5QiPzfyZGIxQmed8VEK2NRSQT3/JX5V1njfQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/protocol-http": "^5.1.1",
				"@smithy/querystring-builder": "^4.0.3",
				"@smithy/types": "^4.3.0",
				"@smithy/util-base64": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/hash-node": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-4.0.3.tgz",
			"integrity": "sha512-W5Uhy6v/aYrgtjh9y0YP332gIQcwccQ+EcfWhllL0B9rPae42JngTTUpb8W6wuxaNFzqps4xq5klHckSSOy5fw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"@smithy/util-buffer-from": "^4.0.0",
				"@smithy/util-utf8": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/invalid-dependency": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-4.0.3.tgz",
			"integrity": "sha512-1Bo8Ur1ZGqxvwTqBmv6DZEn0rXtwJGeqiiO2/JFcCtz3nBakOqeXbJBElXJMMzd0ghe8+eB6Dkw98nMYctgizg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/is-array-buffer": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-4.0.0.tgz",
			"integrity": "sha512-saYhF8ZZNoJDTvJBEWgeBccCg+yvp1CX+ed12yORU3NilJScfc6gfch2oVb4QgxZrGUx3/ZJlb+c/dJbyupxlw==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/middleware-content-length": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-4.0.3.tgz",
			"integrity": "sha512-NE/Zph4BP5u16bzYq2csq9qD0T6UBLeg4AuNrwNJ7Gv9uLYaGEgelZUOdRndGdMGcUfSGvNlXGb2aA2hPCwJ6g==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/protocol-http": "^5.1.1",
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/middleware-endpoint": {
			"version": "4.1.7",
			"resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.7.tgz",
			"integrity": "sha512-KDzM7Iajo6K7eIWNNtukykRT4eWwlHjCEsULZUaSfi/SRSBK8BPRqG5FsVfp58lUxcvre8GT8AIPIqndA0ERKw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/core": "^3.4.0",
				"@smithy/middleware-serde": "^4.0.6",
				"@smithy/node-config-provider": "^4.1.2",
				"@smithy/shared-ini-file-loader": "^4.0.3",
				"@smithy/types": "^4.3.0",
				"@smithy/url-parser": "^4.0.3",
				"@smithy/util-middleware": "^4.0.3",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/middleware-retry": {
			"version": "4.1.8",
			"resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.1.8.tgz",
			"integrity": "sha512-e2OtQgFzzlSG0uCjcJmi02QuFSRTrpT11Eh2EcqqDFy7DYriteHZJkkf+4AsxsrGDugAtPFcWBz1aq06sSX5fQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/node-config-provider": "^4.1.2",
				"@smithy/protocol-http": "^5.1.1",
				"@smithy/service-error-classification": "^4.0.4",
				"@smithy/smithy-client": "^4.3.0",
				"@smithy/types": "^4.3.0",
				"@smithy/util-middleware": "^4.0.3",
				"@smithy/util-retry": "^4.0.4",
				"tslib": "^2.6.2",
				"uuid": "^9.0.1"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/middleware-retry/node_modules/uuid": {
			"version": "9.0.1",
			"resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
			"integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
			"funding": [
				"https://github.com/sponsors/broofa",
				"https://github.com/sponsors/ctavan"
			],
			"license": "MIT",
			"bin": {
				"uuid": "dist/bin/uuid"
			}
		},
		"node_modules/@smithy/middleware-serde": {
			"version": "4.0.6",
			"resolved": "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-4.0.6.tgz",
			"integrity": "sha512-YECyl7uNII+jCr/9qEmCu8xYL79cU0fqjo0qxpcVIU18dAPHam/iYwcknAu4Jiyw1uN+sAx7/SMf/Kmef/Jjsg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/protocol-http": "^5.1.1",
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/middleware-stack": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-4.0.3.tgz",
			"integrity": "sha512-baeV7t4jQfQtFxBADFmnhmqBmqR38dNU5cvEgHcMK/Kp3D3bEI0CouoX2Sr/rGuntR+Eg0IjXdxnGGTc6SbIkw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/node-config-provider": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-4.1.2.tgz",
			"integrity": "sha512-SUvNup8iU1v7fmM8XPk+27m36udmGCfSz+VZP5Gb0aJ3Ne0X28K/25gnsrg3X1rWlhcnhzNUUysKW/Ied46ivQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/property-provider": "^4.0.3",
				"@smithy/shared-ini-file-loader": "^4.0.3",
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/node-http-handler": {
			"version": "4.0.5",
			"resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.0.5.tgz",
			"integrity": "sha512-T7QglZC1vS7SPT44/1qSIAQEx5bFKb3LfO6zw/o4Xzt1eC5HNoH1TkS4lMYA9cWFbacUhx4hRl/blLun4EOCkg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/abort-controller": "^4.0.3",
				"@smithy/protocol-http": "^5.1.1",
				"@smithy/querystring-builder": "^4.0.3",
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/property-provider": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-4.0.3.tgz",
			"integrity": "sha512-Wcn17QNdawJZcZZPBuMuzyBENVi1AXl4TdE0jvzo4vWX2x5df/oMlmr/9M5XAAC6+yae4kWZlOYIsNsgDrMU9A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/protocol-http": {
			"version": "5.1.1",
			"resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-5.1.1.tgz",
			"integrity": "sha512-Vsay2mzq05DwNi9jK01yCFtfvu9HimmgC7a4HTs7lhX12Sx8aWsH0mfz6q/02yspSp+lOB+Q2HJwi4IV2GKz7A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/querystring-builder": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-4.0.3.tgz",
			"integrity": "sha512-UUzIWMVfPmDZcOutk2/r1vURZqavvQW0OHvgsyNV0cKupChvqg+/NKPRMaMEe+i8tP96IthMFeZOZWpV+E4RAw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"@smithy/util-uri-escape": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/querystring-parser": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-4.0.3.tgz",
			"integrity": "sha512-K5M4ZJQpFCblOJ5Oyw7diICpFg1qhhR47m2/5Ef1PhGE19RaIZf50tjYFrxa6usqcuXyTiFPGo4d1geZdH4YcQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/service-error-classification": {
			"version": "4.0.4",
			"resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.0.4.tgz",
			"integrity": "sha512-W5ScbQ1bTzgH91kNEE2CvOzM4gXlDOqdow4m8vMFSIXCel2scbHwjflpVNnC60Y3F1m5i7w2gQg9lSnR+JsJAA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/shared-ini-file-loader": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-4.0.3.tgz",
			"integrity": "sha512-vHwlrqhZGIoLwaH8vvIjpHnloShqdJ7SUPNM2EQtEox+yEDFTVQ7E+DLZ+6OhnYEgFUwPByJyz6UZaOu2tny6A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/signature-v4": {
			"version": "5.1.1",
			"resolved": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-5.1.1.tgz",
			"integrity": "sha512-zy8Repr5zvT0ja+Tf5wjV/Ba6vRrhdiDcp/ww6cvqYbSEudIkziDe3uppNRlFoCViyJXdPnLcwyZdDLA4CHzSg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/is-array-buffer": "^4.0.0",
				"@smithy/protocol-http": "^5.1.1",
				"@smithy/types": "^4.3.0",
				"@smithy/util-hex-encoding": "^4.0.0",
				"@smithy/util-middleware": "^4.0.3",
				"@smithy/util-uri-escape": "^4.0.0",
				"@smithy/util-utf8": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/smithy-client": {
			"version": "4.3.0",
			"resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.3.0.tgz",
			"integrity": "sha512-DNsRA38pN6tYHUjebmwD9e4KcgqTLldYQb2gC6K+oxXYdCTxPn6wV9+FvOa6wrU2FQEnGJoi+3GULzOTKck/tg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/core": "^3.4.0",
				"@smithy/middleware-endpoint": "^4.1.7",
				"@smithy/middleware-stack": "^4.0.3",
				"@smithy/protocol-http": "^5.1.1",
				"@smithy/types": "^4.3.0",
				"@smithy/util-stream": "^4.2.1",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/types": {
			"version": "4.3.0",
			"resolved": "https://registry.npmjs.org/@smithy/types/-/types-4.3.0.tgz",
			"integrity": "sha512-+1iaIQHthDh9yaLhRzaoQxRk+l9xlk+JjMFxGRhNLz+m9vKOkjNeU8QuB4w3xvzHyVR/BVlp/4AXDHjoRIkfgQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/url-parser": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-4.0.3.tgz",
			"integrity": "sha512-n5/DnosDu/tweOqUUNtUbu7eRIR4J/Wz9nL7V5kFYQQVb8VYdj7a4G5NJHCw6o21ul7CvZoJkOpdTnsQDLT0tQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/querystring-parser": "^4.0.3",
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-base64": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-4.0.0.tgz",
			"integrity": "sha512-CvHfCmO2mchox9kjrtzoHkWHxjHZzaFojLc8quxXY7WAAMAg43nuxwv95tATVgQFNDwd4M9S1qFzj40Ul41Kmg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/util-buffer-from": "^4.0.0",
				"@smithy/util-utf8": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-body-length-browser": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-4.0.0.tgz",
			"integrity": "sha512-sNi3DL0/k64/LO3A256M+m3CDdG6V7WKWHdAiBBMUN8S3hK3aMPhwnPik2A/a2ONN+9doY9UxaLfgqsIRg69QA==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-body-length-node": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-4.0.0.tgz",
			"integrity": "sha512-q0iDP3VsZzqJyje8xJWEJCNIu3lktUGVoSy1KB0UWym2CL1siV3artm+u1DFYTLejpsrdGyCSWBdGNjJzfDPjg==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-buffer-from": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-4.0.0.tgz",
			"integrity": "sha512-9TOQ7781sZvddgO8nxueKi3+yGvkY35kotA0Y6BWRajAv8jjmigQ1sBwz0UX47pQMYXJPahSKEKYFgt+rXdcug==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/is-array-buffer": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-config-provider": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-4.0.0.tgz",
			"integrity": "sha512-L1RBVzLyfE8OXH+1hsJ8p+acNUSirQnWQ6/EgpchV88G6zGBTDPdXiiExei6Z1wR2RxYvxY/XLw6AMNCCt8H3w==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-defaults-mode-browser": {
			"version": "4.0.15",
			"resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.15.tgz",
			"integrity": "sha512-bJJ/B8owQbHAflatSq92f9OcV8858DJBQF1Y3GRjB8psLyUjbISywszYPFw16beREHO/C3I3taW4VGH+tOuwrQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/property-provider": "^4.0.3",
				"@smithy/smithy-client": "^4.3.0",
				"@smithy/types": "^4.3.0",
				"bowser": "^2.11.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-defaults-mode-node": {
			"version": "4.0.15",
			"resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.15.tgz",
			"integrity": "sha512-8CUrEW2Ni5q+NmYkj8wsgkfqoP7l4ZquptFbq92yQE66xevc4SxqP2zH6tMtN158kgBqBDsZ+qlrRwXWOjCR8A==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/config-resolver": "^4.1.3",
				"@smithy/credential-provider-imds": "^4.0.5",
				"@smithy/node-config-provider": "^4.1.2",
				"@smithy/property-provider": "^4.0.3",
				"@smithy/smithy-client": "^4.3.0",
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-endpoints": {
			"version": "3.0.5",
			"resolved": "https://registry.npmjs.org/@smithy/util-endpoints/-/util-endpoints-3.0.5.tgz",
			"integrity": "sha512-PjDpqLk24/vAl340tmtCA++Q01GRRNH9cwL9qh46NspAX9S+IQVcK+GOzPt0GLJ6KYGyn8uOgo2kvJhiThclJw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/node-config-provider": "^4.1.2",
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-hex-encoding": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-4.0.0.tgz",
			"integrity": "sha512-Yk5mLhHtfIgW2W2WQZWSg5kuMZCVbvhFmC7rV4IO2QqnZdbEFPmQnCcGMAX2z/8Qj3B9hYYNjZOhWym+RwhePw==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-middleware": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-4.0.3.tgz",
			"integrity": "sha512-iIsC6qZXxkD7V3BzTw3b1uK8RVC1M8WvwNxK1PKrH9FnxntCd30CSunXjL/8iJBE8Z0J14r2P69njwIpRG4FBQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-retry": {
			"version": "4.0.4",
			"resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.0.4.tgz",
			"integrity": "sha512-Aoqr9W2jDYGrI6OxljN8VmLDQIGO4VdMAUKMf9RGqLG8hn6or+K41NEy1Y5dtum9q8F7e0obYAuKl2mt/GnpZg==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/service-error-classification": "^4.0.4",
				"@smithy/types": "^4.3.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-stream": {
			"version": "4.2.1",
			"resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.2.1.tgz",
			"integrity": "sha512-W3IR0x5DY6iVtjj5p902oNhD+Bz7vs5S+p6tppbPa509rV9BdeXZjGuRSCtVEad9FA0Mba+tNUtUmtnSI1nwUw==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/fetch-http-handler": "^5.0.3",
				"@smithy/node-http-handler": "^4.0.5",
				"@smithy/types": "^4.3.0",
				"@smithy/util-base64": "^4.0.0",
				"@smithy/util-buffer-from": "^4.0.0",
				"@smithy/util-hex-encoding": "^4.0.0",
				"@smithy/util-utf8": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-uri-escape": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-4.0.0.tgz",
			"integrity": "sha512-77yfbCbQMtgtTylO9itEAdpPXSog3ZxMe09AEhm0dU0NLTalV70ghDZFR+Nfi1C60jnJoh/Re4090/DuZh2Omg==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@smithy/util-utf8": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-4.0.0.tgz",
			"integrity": "sha512-b+zebfKCfRdgNJDknHCob3O7FpeYQN6ZG6YLExMcasDHsCXlsXCEuiPZeLnJLpwa5dvPetGlnGCiMHuLwGvFow==",
			"license": "Apache-2.0",
			"dependencies": {
				"@smithy/util-buffer-from": "^4.0.0",
				"tslib": "^2.6.2"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/@standard-schema/spec": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/@standard-schema/spec/-/spec-1.0.0.tgz",
			"integrity": "sha512-m2bOd0f2RT9k8QJx1JN85cZYyH1RqFBdlwtkSlf4tBDYLCiiZnv1fIIwacK6cqwXavOydf0NPToMQgpKq+dVlA==",
			"license": "MIT"
		},
		"node_modules/@tokenizer/inflate": {
			"version": "0.2.7",
			"resolved": "https://registry.npmjs.org/@tokenizer/inflate/-/inflate-0.2.7.tgz",
			"integrity": "sha512-MADQgmZT1eKjp06jpI2yozxaU9uVs4GzzgSL+uEq7bVcJ9V1ZXQkeGNql1fsSI0gMy1vhvNTNbUqrx+pZfJVmg==",
			"license": "MIT",
			"dependencies": {
				"debug": "^4.4.0",
				"fflate": "^0.8.2",
				"token-types": "^6.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"type": "github",
				"url": "https://github.com/sponsors/Borewit"
			}
		},
		"node_modules/@tokenizer/token": {
			"version": "0.3.0",
			"resolved": "https://registry.npmjs.org/@tokenizer/token/-/token-0.3.0.tgz",
			"integrity": "sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==",
			"license": "MIT"
		},
		"node_modules/@types/babel__core": {
			"version": "7.20.5",
			"resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz",
			"integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/parser": "^7.20.7",
				"@babel/types": "^7.20.7",
				"@types/babel__generator": "*",
				"@types/babel__template": "*",
				"@types/babel__traverse": "*"
			}
		},
		"node_modules/@types/babel__generator": {
			"version": "7.6.8",
			"resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz",
			"integrity": "sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/types": "^7.0.0"
			}
		},
		"node_modules/@types/babel__template": {
			"version": "7.4.4",
			"resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz",
			"integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/parser": "^7.1.0",
				"@babel/types": "^7.0.0"
			}
		},
		"node_modules/@types/babel__traverse": {
			"version": "7.20.6",
			"resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.6.tgz",
			"integrity": "sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/types": "^7.20.7"
			}
		},
		"node_modules/@types/diff-match-patch": {
			"version": "1.0.36",
			"resolved": "https://registry.npmjs.org/@types/diff-match-patch/-/diff-match-patch-1.0.36.tgz",
			"integrity": "sha512-xFdR6tkm0MWvBfO8xXCSsinYxHcqkQUlcHeSpMC2ukzOb6lwQAfDmW+Qt0AvlGd8HpsS28qKsB+oPeJn9I39jg==",
			"license": "MIT"
		},
		"node_modules/@types/graceful-fs": {
			"version": "4.1.9",
			"resolved": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz",
			"integrity": "sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@types/node": "*"
			}
		},
		"node_modules/@types/istanbul-lib-coverage": {
			"version": "2.0.6",
			"resolved": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz",
			"integrity": "sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@types/istanbul-lib-report": {
			"version": "3.0.3",
			"resolved": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz",
			"integrity": "sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@types/istanbul-lib-coverage": "*"
			}
		},
		"node_modules/@types/istanbul-reports": {
			"version": "3.0.4",
			"resolved": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz",
			"integrity": "sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@types/istanbul-lib-report": "*"
			}
		},
		"node_modules/@types/jest": {
			"version": "29.5.14",
			"resolved": "https://registry.npmjs.org/@types/jest/-/jest-29.5.14.tgz",
			"integrity": "sha512-ZN+4sdnLUbo8EVvVc2ao0GFW6oVrQRPn4K2lglySj7APvSrgzxHiNNK99us4WDMi57xxA2yggblIAMNhXOotLQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"expect": "^29.0.0",
				"pretty-format": "^29.0.0"
			}
		},
		"node_modules/@types/node": {
			"version": "18.19.81",
			"resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.81.tgz",
			"integrity": "sha512-7KO9oZ2//ivtSsryp0LQUqq79zyGXzwq1WqfywpC9ucjY7YyltMMmxWgtRFRKCxwa7VPxVBVy4kHf5UC1E8Lug==",
			"license": "MIT",
			"dependencies": {
				"undici-types": "~5.26.4"
			}
		},
		"node_modules/@types/node-fetch": {
			"version": "2.6.12",
			"resolved": "https://registry.npmjs.org/@types/node-fetch/-/node-fetch-2.6.12.tgz",
			"integrity": "sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==",
			"license": "MIT",
			"dependencies": {
				"@types/node": "*",
				"form-data": "^4.0.0"
			}
		},
		"node_modules/@types/stack-utils": {
			"version": "2.0.3",
			"resolved": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz",
			"integrity": "sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/@types/tinycolor2": {
			"version": "1.4.6",
			"resolved": "https://registry.npmjs.org/@types/tinycolor2/-/tinycolor2-1.4.6.tgz",
			"integrity": "sha512-iEN8J0BoMnsWBqjVbWH/c0G0Hh7O21lpR2/+PrvAVgWdzL7eexIFm4JN/Wn10PTcmNdtS6U67r499mlWMXOxNw==",
			"license": "MIT"
		},
		"node_modules/@types/yargs": {
			"version": "17.0.33",
			"resolved": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz",
			"integrity": "sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@types/yargs-parser": "*"
			}
		},
		"node_modules/@types/yargs-parser": {
			"version": "21.0.3",
			"resolved": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz",
			"integrity": "sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/abort-controller": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz",
			"integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==",
			"license": "MIT",
			"dependencies": {
				"event-target-shim": "^5.0.0"
			},
			"engines": {
				"node": ">=6.5"
			}
		},
		"node_modules/accepts": {
			"version": "1.3.8",
			"resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz",
			"integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==",
			"license": "MIT",
			"dependencies": {
				"mime-types": "~2.1.34",
				"negotiator": "0.6.3"
			},
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/agent-base": {
			"version": "7.1.3",
			"resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz",
			"integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==",
			"license": "MIT",
			"engines": {
				"node": ">= 14"
			}
		},
		"node_modules/agentkeepalive": {
			"version": "4.6.0",
			"resolved": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.6.0.tgz",
			"integrity": "sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==",
			"license": "MIT",
			"dependencies": {
				"humanize-ms": "^1.2.1"
			},
			"engines": {
				"node": ">= 8.0.0"
			}
		},
		"node_modules/ai": {
			"version": "4.3.10",
			"resolved": "https://registry.npmjs.org/ai/-/ai-4.3.10.tgz",
			"integrity": "sha512-jw+ahNu+T4SHj9gtraIKtYhanJI6gj2IZ5BFcfEHgoyQVMln5a5beGjzl/nQSX6FxyLqJ/UBpClRa279EEKK/Q==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "1.1.3",
				"@ai-sdk/provider-utils": "2.2.7",
				"@ai-sdk/react": "1.2.9",
				"@ai-sdk/ui-utils": "1.2.8",
				"@opentelemetry/api": "1.9.0",
				"jsondiffpatch": "0.6.0"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"react": "^18 || ^19 || ^19.0.0-rc",
				"zod": "^3.23.8"
			},
			"peerDependenciesMeta": {
				"react": {
					"optional": true
				}
			}
		},
		"node_modules/ajv": {
			"version": "6.12.6",
			"resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz",
			"integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==",
			"license": "MIT",
			"dependencies": {
				"fast-deep-equal": "^3.1.1",
				"fast-json-stable-stringify": "^2.0.0",
				"json-schema-traverse": "^0.4.1",
				"uri-js": "^4.2.2"
			},
			"funding": {
				"type": "github",
				"url": "https://github.com/sponsors/epoberezkin"
			}
		},
		"node_modules/ansi-align": {
			"version": "3.0.1",
			"resolved": "https://registry.npmjs.org/ansi-align/-/ansi-align-3.0.1.tgz",
			"integrity": "sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==",
			"license": "ISC",
			"dependencies": {
				"string-width": "^4.1.0"
			}
		},
		"node_modules/ansi-align/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/ansi-align/node_modules/emoji-regex": {
			"version": "8.0.0",
			"resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
			"integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
			"license": "MIT"
		},
		"node_modules/ansi-align/node_modules/string-width": {
			"version": "4.2.3",
			"resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
			"integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
			"license": "MIT",
			"dependencies": {
				"emoji-regex": "^8.0.0",
				"is-fullwidth-code-point": "^3.0.0",
				"strip-ansi": "^6.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/ansi-align/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/ansi-colors": {
			"version": "4.1.3",
			"resolved": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz",
			"integrity": "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/ansi-escapes": {
			"version": "4.3.2",
			"resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz",
			"integrity": "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==",
			"license": "MIT",
			"dependencies": {
				"type-fest": "^0.21.3"
			},
			"engines": {
				"node": ">=8"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/ansi-escapes/node_modules/type-fest": {
			"version": "0.21.3",
			"resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz",
			"integrity": "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==",
			"license": "(MIT OR CC0-1.0)",
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/ansi-regex": {
			"version": "6.1.0",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz",
			"integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/chalk/ansi-regex?sponsor=1"
			}
		},
		"node_modules/ansi-styles": {
			"version": "4.3.0",
			"resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz",
			"integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",
			"license": "MIT",
			"dependencies": {
				"color-convert": "^2.0.1"
			},
			"engines": {
				"node": ">=8"
			},
			"funding": {
				"url": "https://github.com/chalk/ansi-styles?sponsor=1"
			}
		},
		"node_modules/anymatch": {
			"version": "3.1.3",
			"resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz",
			"integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"normalize-path": "^3.0.0",
				"picomatch": "^2.0.4"
			},
			"engines": {
				"node": ">= 8"
			}
		},
		"node_modules/argparse": {
			"version": "1.0.10",
			"resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz",
			"integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"sprintf-js": "~1.0.2"
			}
		},
		"node_modules/array-flatten": {
			"version": "1.1.1",
			"resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz",
			"integrity": "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==",
			"license": "MIT"
		},
		"node_modules/array-union": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz",
			"integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/asap": {
			"version": "2.0.6",
			"resolved": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz",
			"integrity": "sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/asynckit": {
			"version": "0.4.0",
			"resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz",
			"integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==",
			"license": "MIT"
		},
		"node_modules/auto-bind": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/auto-bind/-/auto-bind-5.0.1.tgz",
			"integrity": "sha512-ooviqdwwgfIfNmDwo94wlshcdzfO64XV0Cg6oDsDYBJfITDz1EngD2z7DkbvCWn+XIMsIqW27sEVF6qcpJrRcg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": "^12.20.0 || ^14.13.1 || >=16.0.0"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/aws4fetch": {
			"version": "1.0.20",
			"resolved": "https://registry.npmjs.org/aws4fetch/-/aws4fetch-1.0.20.tgz",
			"integrity": "sha512-/djoAN709iY65ETD6LKCtyyEI04XIBP5xVvfmNxsEP0uJB5tyaGBztSryRr4HqMStr9R06PisQE7m9zDTXKu6g==",
			"license": "MIT"
		},
		"node_modules/babel-jest": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz",
			"integrity": "sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/transform": "^29.7.0",
				"@types/babel__core": "^7.1.14",
				"babel-plugin-istanbul": "^6.1.1",
				"babel-preset-jest": "^29.6.3",
				"chalk": "^4.0.0",
				"graceful-fs": "^4.2.9",
				"slash": "^3.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.8.0"
			}
		},
		"node_modules/babel-jest/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/babel-plugin-istanbul": {
			"version": "6.1.1",
			"resolved": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz",
			"integrity": "sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"@babel/helper-plugin-utils": "^7.0.0",
				"@istanbuljs/load-nyc-config": "^1.0.0",
				"@istanbuljs/schema": "^0.1.2",
				"istanbul-lib-instrument": "^5.0.4",
				"test-exclude": "^6.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument": {
			"version": "5.2.1",
			"resolved": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz",
			"integrity": "sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"@babel/core": "^7.12.3",
				"@babel/parser": "^7.14.7",
				"@istanbuljs/schema": "^0.1.2",
				"istanbul-lib-coverage": "^3.2.0",
				"semver": "^6.3.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/babel-plugin-jest-hoist": {
			"version": "29.6.3",
			"resolved": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz",
			"integrity": "sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/template": "^7.3.3",
				"@babel/types": "^7.3.3",
				"@types/babel__core": "^7.1.14",
				"@types/babel__traverse": "^7.0.6"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/babel-preset-current-node-syntax": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz",
			"integrity": "sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/plugin-syntax-async-generators": "^7.8.4",
				"@babel/plugin-syntax-bigint": "^7.8.3",
				"@babel/plugin-syntax-class-properties": "^7.12.13",
				"@babel/plugin-syntax-class-static-block": "^7.14.5",
				"@babel/plugin-syntax-import-attributes": "^7.24.7",
				"@babel/plugin-syntax-import-meta": "^7.10.4",
				"@babel/plugin-syntax-json-strings": "^7.8.3",
				"@babel/plugin-syntax-logical-assignment-operators": "^7.10.4",
				"@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3",
				"@babel/plugin-syntax-numeric-separator": "^7.10.4",
				"@babel/plugin-syntax-object-rest-spread": "^7.8.3",
				"@babel/plugin-syntax-optional-catch-binding": "^7.8.3",
				"@babel/plugin-syntax-optional-chaining": "^7.8.3",
				"@babel/plugin-syntax-private-property-in-object": "^7.14.5",
				"@babel/plugin-syntax-top-level-await": "^7.14.5"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0"
			}
		},
		"node_modules/babel-preset-jest": {
			"version": "29.6.3",
			"resolved": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz",
			"integrity": "sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"babel-plugin-jest-hoist": "^29.6.3",
				"babel-preset-current-node-syntax": "^1.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			},
			"peerDependencies": {
				"@babel/core": "^7.0.0"
			}
		},
		"node_modules/balanced-match": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz",
			"integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/base64-js": {
			"version": "1.5.1",
			"resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz",
			"integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==",
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/feross"
				},
				{
					"type": "patreon",
					"url": "https://www.patreon.com/feross"
				},
				{
					"type": "consulting",
					"url": "https://feross.org/support"
				}
			],
			"license": "MIT"
		},
		"node_modules/better-path-resolve": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/better-path-resolve/-/better-path-resolve-1.0.0.tgz",
			"integrity": "sha512-pbnl5XzGBdrFU/wT4jqmJVPn2B6UHPBOhzMQkY/SPUPB6QtUXtmBHBIwCbXJol93mOpGMnQyP/+BB19q04xj7g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"is-windows": "^1.0.0"
			},
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/bignumber.js": {
			"version": "9.3.0",
			"resolved": "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.3.0.tgz",
			"integrity": "sha512-EM7aMFTXbptt/wZdMlBv2t8IViwQL+h6SLHosp8Yf0dqJMTnY6iL32opnAB6kAdL0SZPuvcAzFr31o0c/R3/RA==",
			"license": "MIT",
			"engines": {
				"node": "*"
			}
		},
		"node_modules/body-parser": {
			"version": "1.20.3",
			"resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz",
			"integrity": "sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==",
			"license": "MIT",
			"dependencies": {
				"bytes": "3.1.2",
				"content-type": "~1.0.5",
				"debug": "2.6.9",
				"depd": "2.0.0",
				"destroy": "1.2.0",
				"http-errors": "2.0.0",
				"iconv-lite": "0.4.24",
				"on-finished": "2.4.1",
				"qs": "6.13.0",
				"raw-body": "2.5.2",
				"type-is": "~1.6.18",
				"unpipe": "1.0.0"
			},
			"engines": {
				"node": ">= 0.8",
				"npm": "1.2.8000 || >= 1.4.16"
			}
		},
		"node_modules/body-parser/node_modules/debug": {
			"version": "2.6.9",
			"resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz",
			"integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==",
			"license": "MIT",
			"dependencies": {
				"ms": "2.0.0"
			}
		},
		"node_modules/body-parser/node_modules/ms": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz",
			"integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==",
			"license": "MIT"
		},
		"node_modules/body-parser/node_modules/qs": {
			"version": "6.13.0",
			"resolved": "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz",
			"integrity": "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==",
			"license": "BSD-3-Clause",
			"dependencies": {
				"side-channel": "^1.0.6"
			},
			"engines": {
				"node": ">=0.6"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/bowser": {
			"version": "2.11.0",
			"resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz",
			"integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==",
			"license": "MIT"
		},
		"node_modules/boxen": {
			"version": "8.0.1",
			"resolved": "https://registry.npmjs.org/boxen/-/boxen-8.0.1.tgz",
			"integrity": "sha512-F3PH5k5juxom4xktynS7MoFY+NUWH5LC4CnH11YB8NPew+HLpmBLCybSAEyb2F+4pRXhuhWqFesoQd6DAyc2hw==",
			"license": "MIT",
			"dependencies": {
				"ansi-align": "^3.0.1",
				"camelcase": "^8.0.0",
				"chalk": "^5.3.0",
				"cli-boxes": "^3.0.0",
				"string-width": "^7.2.0",
				"type-fest": "^4.21.0",
				"widest-line": "^5.0.0",
				"wrap-ansi": "^9.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/brace-expansion": {
			"version": "1.1.11",
			"resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz",
			"integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"balanced-match": "^1.0.0",
				"concat-map": "0.0.1"
			}
		},
		"node_modules/braces": {
			"version": "3.0.3",
			"resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz",
			"integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"fill-range": "^7.1.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/browserslist": {
			"version": "4.24.4",
			"resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz",
			"integrity": "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==",
			"dev": true,
			"funding": [
				{
					"type": "opencollective",
					"url": "https://opencollective.com/browserslist"
				},
				{
					"type": "tidelift",
					"url": "https://tidelift.com/funding/github/npm/browserslist"
				},
				{
					"type": "github",
					"url": "https://github.com/sponsors/ai"
				}
			],
			"license": "MIT",
			"dependencies": {
				"caniuse-lite": "^1.0.30001688",
				"electron-to-chromium": "^1.5.73",
				"node-releases": "^2.0.19",
				"update-browserslist-db": "^1.1.1"
			},
			"bin": {
				"browserslist": "cli.js"
			},
			"engines": {
				"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"
			}
		},
		"node_modules/bser": {
			"version": "2.1.1",
			"resolved": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz",
			"integrity": "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==",
			"dev": true,
			"license": "Apache-2.0",
			"dependencies": {
				"node-int64": "^0.4.0"
			}
		},
		"node_modules/buffer-equal-constant-time": {
			"version": "1.0.1",
			"resolved": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz",
			"integrity": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==",
			"license": "BSD-3-Clause"
		},
		"node_modules/buffer-from": {
			"version": "1.1.2",
			"resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz",
			"integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/bytes": {
			"version": "3.1.2",
			"resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz",
			"integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/call-bind-apply-helpers": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz",
			"integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==",
			"license": "MIT",
			"dependencies": {
				"es-errors": "^1.3.0",
				"function-bind": "^1.1.2"
			},
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/call-bound": {
			"version": "1.0.4",
			"resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz",
			"integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==",
			"license": "MIT",
			"dependencies": {
				"call-bind-apply-helpers": "^1.0.2",
				"get-intrinsic": "^1.3.0"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/callsites": {
			"version": "3.1.0",
			"resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz",
			"integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/camelcase": {
			"version": "8.0.0",
			"resolved": "https://registry.npmjs.org/camelcase/-/camelcase-8.0.0.tgz",
			"integrity": "sha512-8WB3Jcas3swSvjIeA2yvCJ+Miyz5l1ZmB6HFb9R1317dt9LCQoswg/BGrmAmkWVEszSrrg4RwmO46qIm2OEnSA==",
			"license": "MIT",
			"engines": {
				"node": ">=16"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/caniuse-lite": {
			"version": "1.0.30001707",
			"resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001707.tgz",
			"integrity": "sha512-3qtRjw/HQSMlDWf+X79N206fepf4SOOU6SQLMaq/0KkZLmSjPxAkBOQQ+FxbHKfHmYLZFfdWsO3KA90ceHPSnw==",
			"dev": true,
			"funding": [
				{
					"type": "opencollective",
					"url": "https://opencollective.com/browserslist"
				},
				{
					"type": "tidelift",
					"url": "https://tidelift.com/funding/github/npm/caniuse-lite"
				},
				{
					"type": "github",
					"url": "https://github.com/sponsors/ai"
				}
			],
			"license": "CC-BY-4.0"
		},
		"node_modules/chalk": {
			"version": "5.4.1",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz",
			"integrity": "sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==",
			"license": "MIT",
			"engines": {
				"node": "^12.17.0 || ^14.13 || >=16.0.0"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/char-regex": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz",
			"integrity": "sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/chardet": {
			"version": "0.7.0",
			"resolved": "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz",
			"integrity": "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==",
			"license": "MIT"
		},
		"node_modules/ci-info": {
			"version": "3.9.0",
			"resolved": "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz",
			"integrity": "sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==",
			"dev": true,
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/sibiraj-s"
				}
			],
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/cjs-module-lexer": {
			"version": "1.4.3",
			"resolved": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz",
			"integrity": "sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/cli-boxes": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/cli-boxes/-/cli-boxes-3.0.0.tgz",
			"integrity": "sha512-/lzGpEWL/8PfI0BmBOPRwp0c/wFNX1RdUML3jK/RcSBA9T8mZDdQpqYBKtCFTOfQbwPqWEOpjqW+Fnayc0969g==",
			"license": "MIT",
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/cli-cursor": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-5.0.0.tgz",
			"integrity": "sha512-aCj4O5wKyszjMmDT4tZj93kxyydN/K5zPWSCe6/0AV/AA1pqe5ZBIw0a2ZfPQV7lL5/yb5HsUreJ6UFAF1tEQw==",
			"license": "MIT",
			"dependencies": {
				"restore-cursor": "^5.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/cli-spinners": {
			"version": "2.9.2",
			"resolved": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz",
			"integrity": "sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==",
			"license": "MIT",
			"engines": {
				"node": ">=6"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/cli-table3": {
			"version": "0.6.5",
			"resolved": "https://registry.npmjs.org/cli-table3/-/cli-table3-0.6.5.tgz",
			"integrity": "sha512-+W/5efTR7y5HRD7gACw9yQjqMVvEMLBHmboM/kPWam+H+Hmyrgjh6YncVKK122YZkXrLudzTuAukUw9FnMf7IQ==",
			"license": "MIT",
			"dependencies": {
				"string-width": "^4.2.0"
			},
			"engines": {
				"node": "10.* || >= 12.*"
			},
			"optionalDependencies": {
				"@colors/colors": "1.5.0"
			}
		},
		"node_modules/cli-table3/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/cli-table3/node_modules/emoji-regex": {
			"version": "8.0.0",
			"resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
			"integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
			"license": "MIT"
		},
		"node_modules/cli-table3/node_modules/string-width": {
			"version": "4.2.3",
			"resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
			"integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
			"license": "MIT",
			"dependencies": {
				"emoji-regex": "^8.0.0",
				"is-fullwidth-code-point": "^3.0.0",
				"strip-ansi": "^6.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/cli-table3/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/cli-truncate": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/cli-truncate/-/cli-truncate-4.0.0.tgz",
			"integrity": "sha512-nPdaFdQ0h/GEigbPClz11D0v/ZJEwxmeVZGeMo3Z5StPtUTkA9o1lD6QwoirYiSDzbcwn2XcjwmCp68W1IS4TA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"slice-ansi": "^5.0.0",
				"string-width": "^7.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/cli-truncate/node_modules/ansi-styles": {
			"version": "6.2.1",
			"resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz",
			"integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/chalk/ansi-styles?sponsor=1"
			}
		},
		"node_modules/cli-truncate/node_modules/is-fullwidth-code-point": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz",
			"integrity": "sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/cli-truncate/node_modules/slice-ansi": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-5.0.0.tgz",
			"integrity": "sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^6.0.0",
				"is-fullwidth-code-point": "^4.0.0"
			},
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/chalk/slice-ansi?sponsor=1"
			}
		},
		"node_modules/cli-width": {
			"version": "4.1.0",
			"resolved": "https://registry.npmjs.org/cli-width/-/cli-width-4.1.0.tgz",
			"integrity": "sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ==",
			"license": "ISC",
			"engines": {
				"node": ">= 12"
			}
		},
		"node_modules/cliui": {
			"version": "8.0.1",
			"resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz",
			"integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==",
			"license": "ISC",
			"dependencies": {
				"string-width": "^4.2.0",
				"strip-ansi": "^6.0.1",
				"wrap-ansi": "^7.0.0"
			},
			"engines": {
				"node": ">=12"
			}
		},
		"node_modules/cliui/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/cliui/node_modules/emoji-regex": {
			"version": "8.0.0",
			"resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
			"integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
			"license": "MIT"
		},
		"node_modules/cliui/node_modules/string-width": {
			"version": "4.2.3",
			"resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
			"integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
			"license": "MIT",
			"dependencies": {
				"emoji-regex": "^8.0.0",
				"is-fullwidth-code-point": "^3.0.0",
				"strip-ansi": "^6.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/cliui/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/cliui/node_modules/wrap-ansi": {
			"version": "7.0.0",
			"resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz",
			"integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==",
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.0.0",
				"string-width": "^4.1.0",
				"strip-ansi": "^6.0.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/wrap-ansi?sponsor=1"
			}
		},
		"node_modules/co": {
			"version": "4.6.0",
			"resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz",
			"integrity": "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"iojs": ">= 1.0.0",
				"node": ">= 0.12.0"
			}
		},
		"node_modules/code-excerpt": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/code-excerpt/-/code-excerpt-4.0.0.tgz",
			"integrity": "sha512-xxodCmBen3iy2i0WtAK8FlFNrRzjUqjRsMfho58xT/wvZU1YTM3fCnRjcy1gJPMepaRlgm/0e6w8SpWHpn3/cA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"convert-to-spaces": "^2.0.1"
			},
			"engines": {
				"node": "^12.20.0 || ^14.13.1 || >=16.0.0"
			}
		},
		"node_modules/collect-v8-coverage": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz",
			"integrity": "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/color-convert": {
			"version": "2.0.1",
			"resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz",
			"integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==",
			"license": "MIT",
			"dependencies": {
				"color-name": "~1.1.4"
			},
			"engines": {
				"node": ">=7.0.0"
			}
		},
		"node_modules/color-name": {
			"version": "1.1.4",
			"resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz",
			"integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==",
			"license": "MIT"
		},
		"node_modules/combined-stream": {
			"version": "1.0.8",
			"resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz",
			"integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==",
			"license": "MIT",
			"dependencies": {
				"delayed-stream": "~1.0.0"
			},
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/commander": {
			"version": "11.1.0",
			"resolved": "https://registry.npmjs.org/commander/-/commander-11.1.0.tgz",
			"integrity": "sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ==",
			"license": "MIT",
			"engines": {
				"node": ">=16"
			}
		},
		"node_modules/component-emitter": {
			"version": "1.3.1",
			"resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.1.tgz",
			"integrity": "sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==",
			"dev": true,
			"license": "MIT",
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/concat-map": {
			"version": "0.0.1",
			"resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz",
			"integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/content-disposition": {
			"version": "0.5.4",
			"resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz",
			"integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==",
			"license": "MIT",
			"dependencies": {
				"safe-buffer": "5.2.1"
			},
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/content-type": {
			"version": "1.0.5",
			"resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz",
			"integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/convert-source-map": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz",
			"integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/convert-to-spaces": {
			"version": "2.0.1",
			"resolved": "https://registry.npmjs.org/convert-to-spaces/-/convert-to-spaces-2.0.1.tgz",
			"integrity": "sha512-rcQ1bsQO9799wq24uE5AM2tAILy4gXGIK/njFWcVQkGNZ96edlpY+A7bjwvzjYvLDyzmG1MmMLZhpcsb+klNMQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": "^12.20.0 || ^14.13.1 || >=16.0.0"
			}
		},
		"node_modules/cookie": {
			"version": "0.7.1",
			"resolved": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz",
			"integrity": "sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/cookie-signature": {
			"version": "1.0.6",
			"resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz",
			"integrity": "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==",
			"license": "MIT"
		},
		"node_modules/cookiejar": {
			"version": "2.1.4",
			"resolved": "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.4.tgz",
			"integrity": "sha512-LDx6oHrK+PhzLKJU9j5S7/Y3jM/mUHvD/DeI1WQmJn652iPC5Y4TBzC9l+5OMOXlyTTA+SmVUPm0HQUwpD5Jqw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/cors": {
			"version": "2.8.5",
			"resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz",
			"integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==",
			"license": "MIT",
			"dependencies": {
				"object-assign": "^4",
				"vary": "^1"
			},
			"engines": {
				"node": ">= 0.10"
			}
		},
		"node_modules/create-jest": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz",
			"integrity": "sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/types": "^29.6.3",
				"chalk": "^4.0.0",
				"exit": "^0.1.2",
				"graceful-fs": "^4.2.9",
				"jest-config": "^29.7.0",
				"jest-util": "^29.7.0",
				"prompts": "^2.0.1"
			},
			"bin": {
				"create-jest": "bin/create-jest.js"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/create-jest/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/cross-spawn": {
			"version": "7.0.6",
			"resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz",
			"integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==",
			"license": "MIT",
			"dependencies": {
				"path-key": "^3.1.0",
				"shebang-command": "^2.0.0",
				"which": "^2.0.1"
			},
			"engines": {
				"node": ">= 8"
			}
		},
		"node_modules/dataloader": {
			"version": "1.4.0",
			"resolved": "https://registry.npmjs.org/dataloader/-/dataloader-1.4.0.tgz",
			"integrity": "sha512-68s5jYdlvasItOJnCuI2Q9s4q98g0pCyL3HrcKJu8KNugUl8ahgmZYg38ysLTgQjjXX3H8CJLkAvWrclWfcalw==",
			"dev": true,
			"license": "BSD-3-Clause"
		},
		"node_modules/debug": {
			"version": "4.4.0",
			"resolved": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz",
			"integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==",
			"license": "MIT",
			"dependencies": {
				"ms": "^2.1.3"
			},
			"engines": {
				"node": ">=6.0"
			},
			"peerDependenciesMeta": {
				"supports-color": {
					"optional": true
				}
			}
		},
		"node_modules/dedent": {
			"version": "1.5.3",
			"resolved": "https://registry.npmjs.org/dedent/-/dedent-1.5.3.tgz",
			"integrity": "sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==",
			"dev": true,
			"license": "MIT",
			"peerDependencies": {
				"babel-plugin-macros": "^3.1.0"
			},
			"peerDependenciesMeta": {
				"babel-plugin-macros": {
					"optional": true
				}
			}
		},
		"node_modules/deepmerge": {
			"version": "4.3.1",
			"resolved": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz",
			"integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/delayed-stream": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz",
			"integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==",
			"license": "MIT",
			"engines": {
				"node": ">=0.4.0"
			}
		},
		"node_modules/depd": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz",
			"integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/dequal": {
			"version": "2.0.3",
			"resolved": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz",
			"integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==",
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/destroy": {
			"version": "1.2.0",
			"resolved": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz",
			"integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8",
				"npm": "1.2.8000 || >= 1.4.16"
			}
		},
		"node_modules/detect-indent": {
			"version": "6.1.0",
			"resolved": "https://registry.npmjs.org/detect-indent/-/detect-indent-6.1.0.tgz",
			"integrity": "sha512-reYkTUJAZb9gUuZ2RvVCNhVHdg62RHnJ7WJl8ftMi4diZ6NWlciOzQN88pUhSELEwflJht4oQDv0F0BMlwaYtA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/detect-newline": {
			"version": "3.1.0",
			"resolved": "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz",
			"integrity": "sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/dezalgo": {
			"version": "1.0.4",
			"resolved": "https://registry.npmjs.org/dezalgo/-/dezalgo-1.0.4.tgz",
			"integrity": "sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"asap": "^2.0.0",
				"wrappy": "1"
			}
		},
		"node_modules/diff-match-patch": {
			"version": "1.0.5",
			"resolved": "https://registry.npmjs.org/diff-match-patch/-/diff-match-patch-1.0.5.tgz",
			"integrity": "sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw==",
			"license": "Apache-2.0"
		},
		"node_modules/diff-sequences": {
			"version": "29.6.3",
			"resolved": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz",
			"integrity": "sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/dir-glob": {
			"version": "3.0.1",
			"resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz",
			"integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"path-type": "^4.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/dotenv": {
			"version": "16.4.7",
			"resolved": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.7.tgz",
			"integrity": "sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==",
			"license": "BSD-2-Clause",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://dotenvx.com"
			}
		},
		"node_modules/dunder-proto": {
			"version": "1.0.1",
			"resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz",
			"integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==",
			"license": "MIT",
			"dependencies": {
				"call-bind-apply-helpers": "^1.0.1",
				"es-errors": "^1.3.0",
				"gopd": "^1.2.0"
			},
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/ecdsa-sig-formatter": {
			"version": "1.0.11",
			"resolved": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz",
			"integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"safe-buffer": "^5.0.1"
			}
		},
		"node_modules/ee-first": {
			"version": "1.1.1",
			"resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz",
			"integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==",
			"license": "MIT"
		},
		"node_modules/electron-to-chromium": {
			"version": "1.5.123",
			"resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.123.tgz",
			"integrity": "sha512-refir3NlutEZqlKaBLK0tzlVLe5P2wDKS7UQt/3SpibizgsRAPOsqQC3ffw1nlv3ze5gjRQZYHoPymgVZkplFA==",
			"dev": true,
			"license": "ISC"
		},
		"node_modules/emittery": {
			"version": "0.13.1",
			"resolved": "https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz",
			"integrity": "sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sindresorhus/emittery?sponsor=1"
			}
		},
		"node_modules/emoji-regex": {
			"version": "10.4.0",
			"resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.4.0.tgz",
			"integrity": "sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==",
			"license": "MIT"
		},
		"node_modules/encodeurl": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz",
			"integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/enquirer": {
			"version": "2.4.1",
			"resolved": "https://registry.npmjs.org/enquirer/-/enquirer-2.4.1.tgz",
			"integrity": "sha512-rRqJg/6gd538VHvR3PSrdRBb/1Vy2YfzHqzvbhGIQpDRKIa4FgV/54b5Q1xYSxOOwKvjXweS26E0Q+nAMwp2pQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-colors": "^4.1.1",
				"strip-ansi": "^6.0.1"
			},
			"engines": {
				"node": ">=8.6"
			}
		},
		"node_modules/enquirer/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/enquirer/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/environment": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/environment/-/environment-1.1.0.tgz",
			"integrity": "sha512-xUtoPkMggbz0MPyPiIWr1Kp4aeWJjDZ6SMvURhimjdZgsRuDplF5/s9hcgGhyXMhs+6vpnuoiZ2kFiu3FMnS8Q==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/error-ex": {
			"version": "1.3.2",
			"resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz",
			"integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"is-arrayish": "^0.2.1"
			}
		},
		"node_modules/es-define-property": {
			"version": "1.0.1",
			"resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz",
			"integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/es-errors": {
			"version": "1.3.0",
			"resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz",
			"integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/es-object-atoms": {
			"version": "1.1.1",
			"resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz",
			"integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==",
			"license": "MIT",
			"dependencies": {
				"es-errors": "^1.3.0"
			},
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/es-set-tostringtag": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz",
			"integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==",
			"license": "MIT",
			"dependencies": {
				"es-errors": "^1.3.0",
				"get-intrinsic": "^1.2.6",
				"has-tostringtag": "^1.0.2",
				"hasown": "^2.0.2"
			},
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/es-toolkit": {
			"version": "1.36.0",
			"resolved": "https://registry.npmjs.org/es-toolkit/-/es-toolkit-1.36.0.tgz",
			"integrity": "sha512-5lpkRpDELuTSeAL//Rcg5urg+K/yOD1BobJSiNeCc89snMqgrhckmj8jdljqraDbpREiXTNW311RN518eVHBng==",
			"dev": true,
			"license": "MIT",
			"workspaces": [
				"docs",
				"benchmarks"
			]
		},
		"node_modules/esbuild": {
			"version": "0.25.3",
			"resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.3.tgz",
			"integrity": "sha512-qKA6Pvai73+M2FtftpNKRxJ78GIjmFXFxd/1DVBqGo/qNhLSfv+G12n9pNoWdytJC8U00TrViOwpjT0zgqQS8Q==",
			"dev": true,
			"hasInstallScript": true,
			"license": "MIT",
			"bin": {
				"esbuild": "bin/esbuild"
			},
			"engines": {
				"node": ">=18"
			},
			"optionalDependencies": {
				"@esbuild/aix-ppc64": "0.25.3",
				"@esbuild/android-arm": "0.25.3",
				"@esbuild/android-arm64": "0.25.3",
				"@esbuild/android-x64": "0.25.3",
				"@esbuild/darwin-arm64": "0.25.3",
				"@esbuild/darwin-x64": "0.25.3",
				"@esbuild/freebsd-arm64": "0.25.3",
				"@esbuild/freebsd-x64": "0.25.3",
				"@esbuild/linux-arm": "0.25.3",
				"@esbuild/linux-arm64": "0.25.3",
				"@esbuild/linux-ia32": "0.25.3",
				"@esbuild/linux-loong64": "0.25.3",
				"@esbuild/linux-mips64el": "0.25.3",
				"@esbuild/linux-ppc64": "0.25.3",
				"@esbuild/linux-riscv64": "0.25.3",
				"@esbuild/linux-s390x": "0.25.3",
				"@esbuild/linux-x64": "0.25.3",
				"@esbuild/netbsd-arm64": "0.25.3",
				"@esbuild/netbsd-x64": "0.25.3",
				"@esbuild/openbsd-arm64": "0.25.3",
				"@esbuild/openbsd-x64": "0.25.3",
				"@esbuild/sunos-x64": "0.25.3",
				"@esbuild/win32-arm64": "0.25.3",
				"@esbuild/win32-ia32": "0.25.3",
				"@esbuild/win32-x64": "0.25.3"
			}
		},
		"node_modules/escalade": {
			"version": "3.2.0",
			"resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz",
			"integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==",
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/escape-html": {
			"version": "1.0.3",
			"resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz",
			"integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==",
			"license": "MIT"
		},
		"node_modules/escape-string-regexp": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz",
			"integrity": "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/esprima": {
			"version": "4.0.1",
			"resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz",
			"integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==",
			"dev": true,
			"license": "BSD-2-Clause",
			"bin": {
				"esparse": "bin/esparse.js",
				"esvalidate": "bin/esvalidate.js"
			},
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/etag": {
			"version": "1.8.1",
			"resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz",
			"integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/event-target-shim": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz",
			"integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==",
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/eventsource": {
			"version": "3.0.7",
			"resolved": "https://registry.npmjs.org/eventsource/-/eventsource-3.0.7.tgz",
			"integrity": "sha512-CRT1WTyuQoD771GW56XEZFQ/ZoSfWid1alKGDYMmkt2yl8UXrVR4pspqWNEcqKvVIzg6PAltWjxcSSPrboA4iA==",
			"license": "MIT",
			"dependencies": {
				"eventsource-parser": "^3.0.1"
			},
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/eventsource-parser": {
			"version": "3.0.1",
			"resolved": "https://registry.npmjs.org/eventsource-parser/-/eventsource-parser-3.0.1.tgz",
			"integrity": "sha512-VARTJ9CYeuQYb0pZEPbzi740OWFgpHe7AYJ2WFZVnUDUQp5Dk2yJUgF36YsZ81cOyxT0QxmXD2EQpapAouzWVA==",
			"license": "MIT",
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/execa": {
			"version": "8.0.1",
			"resolved": "https://registry.npmjs.org/execa/-/execa-8.0.1.tgz",
			"integrity": "sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"cross-spawn": "^7.0.3",
				"get-stream": "^8.0.1",
				"human-signals": "^5.0.0",
				"is-stream": "^3.0.0",
				"merge-stream": "^2.0.0",
				"npm-run-path": "^5.1.0",
				"onetime": "^6.0.0",
				"signal-exit": "^4.1.0",
				"strip-final-newline": "^3.0.0"
			},
			"engines": {
				"node": ">=16.17"
			},
			"funding": {
				"url": "https://github.com/sindresorhus/execa?sponsor=1"
			}
		},
		"node_modules/execa/node_modules/onetime": {
			"version": "6.0.0",
			"resolved": "https://registry.npmjs.org/onetime/-/onetime-6.0.0.tgz",
			"integrity": "sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"mimic-fn": "^4.0.0"
			},
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/exit": {
			"version": "0.1.2",
			"resolved": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz",
			"integrity": "sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==",
			"dev": true,
			"engines": {
				"node": ">= 0.8.0"
			}
		},
		"node_modules/expect": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/expect/-/expect-29.7.0.tgz",
			"integrity": "sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/expect-utils": "^29.7.0",
				"jest-get-type": "^29.6.3",
				"jest-matcher-utils": "^29.7.0",
				"jest-message-util": "^29.7.0",
				"jest-util": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/express": {
			"version": "4.21.2",
			"resolved": "https://registry.npmjs.org/express/-/express-4.21.2.tgz",
			"integrity": "sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==",
			"license": "MIT",
			"dependencies": {
				"accepts": "~1.3.8",
				"array-flatten": "1.1.1",
				"body-parser": "1.20.3",
				"content-disposition": "0.5.4",
				"content-type": "~1.0.4",
				"cookie": "0.7.1",
				"cookie-signature": "1.0.6",
				"debug": "2.6.9",
				"depd": "2.0.0",
				"encodeurl": "~2.0.0",
				"escape-html": "~1.0.3",
				"etag": "~1.8.1",
				"finalhandler": "1.3.1",
				"fresh": "0.5.2",
				"http-errors": "2.0.0",
				"merge-descriptors": "1.0.3",
				"methods": "~1.1.2",
				"on-finished": "2.4.1",
				"parseurl": "~1.3.3",
				"path-to-regexp": "0.1.12",
				"proxy-addr": "~2.0.7",
				"qs": "6.13.0",
				"range-parser": "~1.2.1",
				"safe-buffer": "5.2.1",
				"send": "0.19.0",
				"serve-static": "1.16.2",
				"setprototypeof": "1.2.0",
				"statuses": "2.0.1",
				"type-is": "~1.6.18",
				"utils-merge": "1.0.1",
				"vary": "~1.1.2"
			},
			"engines": {
				"node": ">= 0.10.0"
			},
			"funding": {
				"type": "opencollective",
				"url": "https://opencollective.com/express"
			}
		},
		"node_modules/express-rate-limit": {
			"version": "7.5.0",
			"resolved": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.5.0.tgz",
			"integrity": "sha512-eB5zbQh5h+VenMPM3fh+nw1YExi5nMr6HUCR62ELSP11huvxm/Uir1H1QEyTkk5QX6A58pX6NmaTMceKZ0Eodg==",
			"license": "MIT",
			"engines": {
				"node": ">= 16"
			},
			"funding": {
				"url": "https://github.com/sponsors/express-rate-limit"
			},
			"peerDependencies": {
				"express": "^4.11 || 5 || ^5.0.0-beta.1"
			}
		},
		"node_modules/express/node_modules/debug": {
			"version": "2.6.9",
			"resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz",
			"integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==",
			"license": "MIT",
			"dependencies": {
				"ms": "2.0.0"
			}
		},
		"node_modules/express/node_modules/ms": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz",
			"integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==",
			"license": "MIT"
		},
		"node_modules/express/node_modules/qs": {
			"version": "6.13.0",
			"resolved": "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz",
			"integrity": "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==",
			"license": "BSD-3-Clause",
			"dependencies": {
				"side-channel": "^1.0.6"
			},
			"engines": {
				"node": ">=0.6"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/extend": {
			"version": "3.0.2",
			"resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz",
			"integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==",
			"license": "MIT"
		},
		"node_modules/extendable-error": {
			"version": "0.1.7",
			"resolved": "https://registry.npmjs.org/extendable-error/-/extendable-error-0.1.7.tgz",
			"integrity": "sha512-UOiS2in6/Q0FK0R0q6UY9vYpQ21mr/Qn1KOnte7vsACuNJf514WvCCUHSRCPcgjPT2bAhNIJdlE6bVap1GKmeg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/external-editor": {
			"version": "3.1.0",
			"resolved": "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz",
			"integrity": "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==",
			"license": "MIT",
			"dependencies": {
				"chardet": "^0.7.0",
				"iconv-lite": "^0.4.24",
				"tmp": "^0.0.33"
			},
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/fast-deep-equal": {
			"version": "3.1.3",
			"resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz",
			"integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==",
			"license": "MIT"
		},
		"node_modules/fast-glob": {
			"version": "3.3.3",
			"resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz",
			"integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@nodelib/fs.stat": "^2.0.2",
				"@nodelib/fs.walk": "^1.2.3",
				"glob-parent": "^5.1.2",
				"merge2": "^1.3.0",
				"micromatch": "^4.0.8"
			},
			"engines": {
				"node": ">=8.6.0"
			}
		},
		"node_modules/fast-json-stable-stringify": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz",
			"integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==",
			"license": "MIT"
		},
		"node_modules/fast-safe-stringify": {
			"version": "2.1.1",
			"resolved": "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz",
			"integrity": "sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/fast-xml-parser": {
			"version": "4.4.1",
			"resolved": "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-4.4.1.tgz",
			"integrity": "sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==",
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/NaturalIntelligence"
				},
				{
					"type": "paypal",
					"url": "https://paypal.me/naturalintelligence"
				}
			],
			"license": "MIT",
			"dependencies": {
				"strnum": "^1.0.5"
			},
			"bin": {
				"fxparser": "src/cli/cli.js"
			}
		},
		"node_modules/fastmcp": {
			"version": "2.2.2",
			"resolved": "https://registry.npmjs.org/fastmcp/-/fastmcp-2.2.2.tgz",
			"integrity": "sha512-V6qEfOnABo7lDrwHqZQhCYd52KXzK85/ipllmUyaos8WLAjygP9NuuKcm1kiEWa0jjsFxe2kf/Y+T4PRE+0rEw==",
			"license": "MIT",
			"dependencies": {
				"@modelcontextprotocol/sdk": "^1.10.2",
				"@standard-schema/spec": "^1.0.0",
				"execa": "^9.5.2",
				"file-type": "^20.4.1",
				"fuse.js": "^7.1.0",
				"mcp-proxy": "^3.0.3",
				"strict-event-emitter-types": "^2.0.0",
				"undici": "^7.8.0",
				"uri-templates": "^0.2.0",
				"xsschema": "0.3.0-beta.1",
				"yargs": "^17.7.2",
				"zod": "^3.25.12",
				"zod-to-json-schema": "^3.24.5"
			},
			"bin": {
				"fastmcp": "dist/bin/fastmcp.js"
			}
		},
		"node_modules/fastmcp/node_modules/execa": {
			"version": "9.5.2",
			"resolved": "https://registry.npmjs.org/execa/-/execa-9.5.2.tgz",
			"integrity": "sha512-EHlpxMCpHWSAh1dgS6bVeoLAXGnJNdR93aabr4QCGbzOM73o5XmRfM/e5FUqsw3aagP8S8XEWUWFAxnRBnAF0Q==",
			"license": "MIT",
			"dependencies": {
				"@sindresorhus/merge-streams": "^4.0.0",
				"cross-spawn": "^7.0.3",
				"figures": "^6.1.0",
				"get-stream": "^9.0.0",
				"human-signals": "^8.0.0",
				"is-plain-obj": "^4.1.0",
				"is-stream": "^4.0.1",
				"npm-run-path": "^6.0.0",
				"pretty-ms": "^9.0.0",
				"signal-exit": "^4.1.0",
				"strip-final-newline": "^4.0.0",
				"yoctocolors": "^2.0.0"
			},
			"engines": {
				"node": "^18.19.0 || >=20.5.0"
			},
			"funding": {
				"url": "https://github.com/sindresorhus/execa?sponsor=1"
			}
		},
		"node_modules/fastmcp/node_modules/get-stream": {
			"version": "9.0.1",
			"resolved": "https://registry.npmjs.org/get-stream/-/get-stream-9.0.1.tgz",
			"integrity": "sha512-kVCxPF3vQM/N0B1PmoqVUqgHP+EeVjmZSQn+1oCRPxd2P21P2F19lIgbR3HBosbB1PUhOAoctJnfEn2GbN2eZA==",
			"license": "MIT",
			"dependencies": {
				"@sec-ant/readable-stream": "^0.4.1",
				"is-stream": "^4.0.1"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/fastmcp/node_modules/human-signals": {
			"version": "8.0.0",
			"resolved": "https://registry.npmjs.org/human-signals/-/human-signals-8.0.0.tgz",
			"integrity": "sha512-/1/GPCpDUCCYwlERiYjxoczfP0zfvZMU/OWgQPMya9AbAE24vseigFdhAMObpc8Q4lc/kjutPfUddDYyAmejnA==",
			"license": "Apache-2.0",
			"engines": {
				"node": ">=18.18.0"
			}
		},
		"node_modules/fastmcp/node_modules/is-stream": {
			"version": "4.0.1",
			"resolved": "https://registry.npmjs.org/is-stream/-/is-stream-4.0.1.tgz",
			"integrity": "sha512-Dnz92NInDqYckGEUJv689RbRiTSEHCQ7wOVeALbkOz999YpqT46yMRIGtSNl2iCL1waAZSx40+h59NV/EwzV/A==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/fastmcp/node_modules/npm-run-path": {
			"version": "6.0.0",
			"resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-6.0.0.tgz",
			"integrity": "sha512-9qny7Z9DsQU8Ou39ERsPU4OZQlSTP47ShQzuKZ6PRXpYLtIFgl/DEBYEXKlvcEa+9tHVcK8CF81Y2V72qaZhWA==",
			"license": "MIT",
			"dependencies": {
				"path-key": "^4.0.0",
				"unicorn-magic": "^0.3.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/fastmcp/node_modules/path-key": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz",
			"integrity": "sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==",
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/fastmcp/node_modules/strip-final-newline": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-4.0.0.tgz",
			"integrity": "sha512-aulFJcD6YK8V1G7iRB5tigAP4TsHBZZrOV8pjV++zdUwmeV8uzbY7yn6h9MswN62adStNZFuCIx4haBnRuMDaw==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/fastq": {
			"version": "1.19.1",
			"resolved": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz",
			"integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"reusify": "^1.0.4"
			}
		},
		"node_modules/fb-watchman": {
			"version": "2.0.2",
			"resolved": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz",
			"integrity": "sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==",
			"dev": true,
			"license": "Apache-2.0",
			"dependencies": {
				"bser": "2.1.1"
			}
		},
		"node_modules/fflate": {
			"version": "0.8.2",
			"resolved": "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz",
			"integrity": "sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A==",
			"license": "MIT"
		},
		"node_modules/figlet": {
			"version": "1.8.0",
			"resolved": "https://registry.npmjs.org/figlet/-/figlet-1.8.0.tgz",
			"integrity": "sha512-chzvGjd+Sp7KUvPHZv6EXV5Ir3Q7kYNpCr4aHrRW79qFtTefmQZNny+W1pW9kf5zeE6dikku2W50W/wAH2xWgw==",
			"license": "MIT",
			"bin": {
				"figlet": "bin/index.js"
			},
			"engines": {
				"node": ">= 0.4.0"
			}
		},
		"node_modules/figures": {
			"version": "6.1.0",
			"resolved": "https://registry.npmjs.org/figures/-/figures-6.1.0.tgz",
			"integrity": "sha512-d+l3qxjSesT4V7v2fh+QnmFnUWv9lSpjarhShNTgBOfA0ttejbQUAlHLitbjkoRiDulW0OPoQPYIGhIC8ohejg==",
			"license": "MIT",
			"dependencies": {
				"is-unicode-supported": "^2.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/file-type": {
			"version": "20.4.1",
			"resolved": "https://registry.npmjs.org/file-type/-/file-type-20.4.1.tgz",
			"integrity": "sha512-hw9gNZXUfZ02Jo0uafWLaFVPter5/k2rfcrjFJJHX/77xtSDOfJuEFb6oKlFV86FLP1SuyHMW1PSk0U9M5tKkQ==",
			"license": "MIT",
			"dependencies": {
				"@tokenizer/inflate": "^0.2.6",
				"strtok3": "^10.2.0",
				"token-types": "^6.0.0",
				"uint8array-extras": "^1.4.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sindresorhus/file-type?sponsor=1"
			}
		},
		"node_modules/fill-range": {
			"version": "7.1.1",
			"resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz",
			"integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"to-regex-range": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/finalhandler": {
			"version": "1.3.1",
			"resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz",
			"integrity": "sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==",
			"license": "MIT",
			"dependencies": {
				"debug": "2.6.9",
				"encodeurl": "~2.0.0",
				"escape-html": "~1.0.3",
				"on-finished": "2.4.1",
				"parseurl": "~1.3.3",
				"statuses": "2.0.1",
				"unpipe": "~1.0.0"
			},
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/finalhandler/node_modules/debug": {
			"version": "2.6.9",
			"resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz",
			"integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==",
			"license": "MIT",
			"dependencies": {
				"ms": "2.0.0"
			}
		},
		"node_modules/finalhandler/node_modules/ms": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz",
			"integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==",
			"license": "MIT"
		},
		"node_modules/find-up": {
			"version": "4.1.0",
			"resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz",
			"integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"locate-path": "^5.0.0",
				"path-exists": "^4.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/form-data": {
			"version": "4.0.2",
			"resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz",
			"integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==",
			"license": "MIT",
			"dependencies": {
				"asynckit": "^0.4.0",
				"combined-stream": "^1.0.8",
				"es-set-tostringtag": "^2.1.0",
				"mime-types": "^2.1.12"
			},
			"engines": {
				"node": ">= 6"
			}
		},
		"node_modules/form-data-encoder": {
			"version": "1.7.2",
			"resolved": "https://registry.npmjs.org/form-data-encoder/-/form-data-encoder-1.7.2.tgz",
			"integrity": "sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==",
			"license": "MIT"
		},
		"node_modules/formdata-node": {
			"version": "4.4.1",
			"resolved": "https://registry.npmjs.org/formdata-node/-/formdata-node-4.4.1.tgz",
			"integrity": "sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==",
			"license": "MIT",
			"dependencies": {
				"node-domexception": "1.0.0",
				"web-streams-polyfill": "4.0.0-beta.3"
			},
			"engines": {
				"node": ">= 12.20"
			}
		},
		"node_modules/formidable": {
			"version": "3.5.2",
			"resolved": "https://registry.npmjs.org/formidable/-/formidable-3.5.2.tgz",
			"integrity": "sha512-Jqc1btCy3QzRbJaICGwKcBfGWuLADRerLzDqi2NwSt/UkXLsHJw2TVResiaoBufHVHy9aSgClOHCeJsSsFLTbg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"dezalgo": "^1.0.4",
				"hexoid": "^2.0.0",
				"once": "^1.4.0"
			},
			"funding": {
				"url": "https://ko-fi.com/tunnckoCore/commissions"
			}
		},
		"node_modules/forwarded": {
			"version": "0.2.0",
			"resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz",
			"integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/fresh": {
			"version": "0.5.2",
			"resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz",
			"integrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/fs-extra": {
			"version": "7.0.1",
			"resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-7.0.1.tgz",
			"integrity": "sha512-YJDaCJZEnBmcbw13fvdAM9AwNOJwOzrE4pqMqBq5nFiEqXUqHwlK4B+3pUw6JNvfSPtX05xFHtYy/1ni01eGCw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"graceful-fs": "^4.1.2",
				"jsonfile": "^4.0.0",
				"universalify": "^0.1.0"
			},
			"engines": {
				"node": ">=6 <7 || >=8"
			}
		},
		"node_modules/fs.realpath": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz",
			"integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==",
			"dev": true,
			"license": "ISC"
		},
		"node_modules/fsevents": {
			"version": "2.3.3",
			"resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz",
			"integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==",
			"dev": true,
			"hasInstallScript": true,
			"license": "MIT",
			"optional": true,
			"os": [
				"darwin"
			],
			"engines": {
				"node": "^8.16.0 || ^10.6.0 || >=11.0.0"
			}
		},
		"node_modules/function-bind": {
			"version": "1.1.2",
			"resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz",
			"integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",
			"license": "MIT",
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/fuse.js": {
			"version": "7.1.0",
			"resolved": "https://registry.npmjs.org/fuse.js/-/fuse.js-7.1.0.tgz",
			"integrity": "sha512-trLf4SzuuUxfusZADLINj+dE8clK1frKdmqiJNb1Es75fmI5oY6X2mxLVUciLLjxqw/xr72Dhy+lER6dGd02FQ==",
			"license": "Apache-2.0",
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/gaxios": {
			"version": "6.7.1",
			"resolved": "https://registry.npmjs.org/gaxios/-/gaxios-6.7.1.tgz",
			"integrity": "sha512-LDODD4TMYx7XXdpwxAVRAIAuB0bzv0s+ywFonY46k126qzQHT9ygyoa9tncmOiQmmDrik65UYsEkv3lbfqQ3yQ==",
			"license": "Apache-2.0",
			"dependencies": {
				"extend": "^3.0.2",
				"https-proxy-agent": "^7.0.1",
				"is-stream": "^2.0.0",
				"node-fetch": "^2.6.9",
				"uuid": "^9.0.1"
			},
			"engines": {
				"node": ">=14"
			}
		},
		"node_modules/gaxios/node_modules/is-stream": {
			"version": "2.0.1",
			"resolved": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz",
			"integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/gaxios/node_modules/node-fetch": {
			"version": "2.7.0",
			"resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz",
			"integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==",
			"license": "MIT",
			"dependencies": {
				"whatwg-url": "^5.0.0"
			},
			"engines": {
				"node": "4.x || >=6.0.0"
			},
			"peerDependencies": {
				"encoding": "^0.1.0"
			},
			"peerDependenciesMeta": {
				"encoding": {
					"optional": true
				}
			}
		},
		"node_modules/gaxios/node_modules/uuid": {
			"version": "9.0.1",
			"resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
			"integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
			"funding": [
				"https://github.com/sponsors/broofa",
				"https://github.com/sponsors/ctavan"
			],
			"license": "MIT",
			"bin": {
				"uuid": "dist/bin/uuid"
			}
		},
		"node_modules/gcp-metadata": {
			"version": "6.1.1",
			"resolved": "https://registry.npmjs.org/gcp-metadata/-/gcp-metadata-6.1.1.tgz",
			"integrity": "sha512-a4tiq7E0/5fTjxPAaH4jpjkSv/uCaU2p5KC6HVGrvl0cDjA8iBZv4vv1gyzlmK0ZUKqwpOyQMKzZQe3lTit77A==",
			"license": "Apache-2.0",
			"dependencies": {
				"gaxios": "^6.1.1",
				"google-logging-utils": "^0.0.2",
				"json-bigint": "^1.0.0"
			},
			"engines": {
				"node": ">=14"
			}
		},
		"node_modules/gensync": {
			"version": "1.0.0-beta.2",
			"resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz",
			"integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6.9.0"
			}
		},
		"node_modules/get-caller-file": {
			"version": "2.0.5",
			"resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz",
			"integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==",
			"license": "ISC",
			"engines": {
				"node": "6.* || 8.* || >= 10.*"
			}
		},
		"node_modules/get-east-asian-width": {
			"version": "1.3.0",
			"resolved": "https://registry.npmjs.org/get-east-asian-width/-/get-east-asian-width-1.3.0.tgz",
			"integrity": "sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/get-intrinsic": {
			"version": "1.3.0",
			"resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz",
			"integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==",
			"license": "MIT",
			"dependencies": {
				"call-bind-apply-helpers": "^1.0.2",
				"es-define-property": "^1.0.1",
				"es-errors": "^1.3.0",
				"es-object-atoms": "^1.1.1",
				"function-bind": "^1.1.2",
				"get-proto": "^1.0.1",
				"gopd": "^1.2.0",
				"has-symbols": "^1.1.0",
				"hasown": "^2.0.2",
				"math-intrinsics": "^1.1.0"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/get-package-type": {
			"version": "0.1.0",
			"resolved": "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz",
			"integrity": "sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8.0.0"
			}
		},
		"node_modules/get-proto": {
			"version": "1.0.1",
			"resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz",
			"integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==",
			"license": "MIT",
			"dependencies": {
				"dunder-proto": "^1.0.1",
				"es-object-atoms": "^1.0.0"
			},
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/get-stream": {
			"version": "8.0.1",
			"resolved": "https://registry.npmjs.org/get-stream/-/get-stream-8.0.1.tgz",
			"integrity": "sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=16"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/get-tsconfig": {
			"version": "4.10.0",
			"resolved": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.0.tgz",
			"integrity": "sha512-kGzZ3LWWQcGIAmg6iWvXn0ei6WDtV26wzHRMwDSzmAbcXrTEXxHy6IehI6/4eT6VRKyMP1eF1VqwrVUmE/LR7A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"resolve-pkg-maps": "^1.0.0"
			},
			"funding": {
				"url": "https://github.com/privatenumber/get-tsconfig?sponsor=1"
			}
		},
		"node_modules/glob": {
			"version": "7.2.3",
			"resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz",
			"integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==",
			"deprecated": "Glob versions prior to v9 are no longer supported",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"fs.realpath": "^1.0.0",
				"inflight": "^1.0.4",
				"inherits": "2",
				"minimatch": "^3.1.1",
				"once": "^1.3.0",
				"path-is-absolute": "^1.0.0"
			},
			"engines": {
				"node": "*"
			},
			"funding": {
				"url": "https://github.com/sponsors/isaacs"
			}
		},
		"node_modules/glob-parent": {
			"version": "5.1.2",
			"resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",
			"integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"is-glob": "^4.0.1"
			},
			"engines": {
				"node": ">= 6"
			}
		},
		"node_modules/globals": {
			"version": "11.12.0",
			"resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz",
			"integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/globby": {
			"version": "11.1.0",
			"resolved": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz",
			"integrity": "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"array-union": "^2.1.0",
				"dir-glob": "^3.0.1",
				"fast-glob": "^3.2.9",
				"ignore": "^5.2.0",
				"merge2": "^1.4.1",
				"slash": "^3.0.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/google-auth-library": {
			"version": "9.15.1",
			"resolved": "https://registry.npmjs.org/google-auth-library/-/google-auth-library-9.15.1.tgz",
			"integrity": "sha512-Jb6Z0+nvECVz+2lzSMt9u98UsoakXxA2HGHMCxh+so3n90XgYWkq5dur19JAJV7ONiJY22yBTyJB1TSkvPq9Ng==",
			"license": "Apache-2.0",
			"dependencies": {
				"base64-js": "^1.3.0",
				"ecdsa-sig-formatter": "^1.0.11",
				"gaxios": "^6.1.1",
				"gcp-metadata": "^6.1.0",
				"gtoken": "^7.0.0",
				"jws": "^4.0.0"
			},
			"engines": {
				"node": ">=14"
			}
		},
		"node_modules/google-auth-library/node_modules/jwa": {
			"version": "2.0.1",
			"resolved": "https://registry.npmjs.org/jwa/-/jwa-2.0.1.tgz",
			"integrity": "sha512-hRF04fqJIP8Abbkq5NKGN0Bbr3JxlQ+qhZufXVr0DvujKy93ZCbXZMHDL4EOtodSbCWxOqR8MS1tXA5hwqCXDg==",
			"license": "MIT",
			"dependencies": {
				"buffer-equal-constant-time": "^1.0.1",
				"ecdsa-sig-formatter": "1.0.11",
				"safe-buffer": "^5.0.1"
			}
		},
		"node_modules/google-auth-library/node_modules/jws": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/jws/-/jws-4.0.0.tgz",
			"integrity": "sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==",
			"license": "MIT",
			"dependencies": {
				"jwa": "^2.0.0",
				"safe-buffer": "^5.0.1"
			}
		},
		"node_modules/google-logging-utils": {
			"version": "0.0.2",
			"resolved": "https://registry.npmjs.org/google-logging-utils/-/google-logging-utils-0.0.2.tgz",
			"integrity": "sha512-NEgUnEcBiP5HrPzufUkBzJOD/Sxsco3rLNo1F1TNf7ieU8ryUzBhqba8r756CjLX7rn3fHl6iLEwPYuqpoKgQQ==",
			"license": "Apache-2.0",
			"engines": {
				"node": ">=14"
			}
		},
		"node_modules/gopd": {
			"version": "1.2.0",
			"resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz",
			"integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/graceful-fs": {
			"version": "4.2.11",
			"resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz",
			"integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==",
			"dev": true,
			"license": "ISC"
		},
		"node_modules/gradient-string": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/gradient-string/-/gradient-string-3.0.0.tgz",
			"integrity": "sha512-frdKI4Qi8Ihp4C6wZNB565de/THpIaw3DjP5ku87M+N9rNSGmPTjfkq61SdRXB7eCaL8O1hkKDvf6CDMtOzIAg==",
			"license": "MIT",
			"dependencies": {
				"chalk": "^5.3.0",
				"tinygradient": "^1.1.5"
			},
			"engines": {
				"node": ">=14"
			}
		},
		"node_modules/gtoken": {
			"version": "7.1.0",
			"resolved": "https://registry.npmjs.org/gtoken/-/gtoken-7.1.0.tgz",
			"integrity": "sha512-pCcEwRi+TKpMlxAQObHDQ56KawURgyAf6jtIY046fJ5tIv3zDe/LEIubckAO8fj6JnAxLdmWkUfNyulQ2iKdEw==",
			"license": "MIT",
			"dependencies": {
				"gaxios": "^6.0.0",
				"jws": "^4.0.0"
			},
			"engines": {
				"node": ">=14.0.0"
			}
		},
		"node_modules/gtoken/node_modules/jwa": {
			"version": "2.0.1",
			"resolved": "https://registry.npmjs.org/jwa/-/jwa-2.0.1.tgz",
			"integrity": "sha512-hRF04fqJIP8Abbkq5NKGN0Bbr3JxlQ+qhZufXVr0DvujKy93ZCbXZMHDL4EOtodSbCWxOqR8MS1tXA5hwqCXDg==",
			"license": "MIT",
			"dependencies": {
				"buffer-equal-constant-time": "^1.0.1",
				"ecdsa-sig-formatter": "1.0.11",
				"safe-buffer": "^5.0.1"
			}
		},
		"node_modules/gtoken/node_modules/jws": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/jws/-/jws-4.0.0.tgz",
			"integrity": "sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==",
			"license": "MIT",
			"dependencies": {
				"jwa": "^2.0.0",
				"safe-buffer": "^5.0.1"
			}
		},
		"node_modules/has-flag": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz",
			"integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/has-symbols": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz",
			"integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/has-tostringtag": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz",
			"integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==",
			"license": "MIT",
			"dependencies": {
				"has-symbols": "^1.0.3"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/hasown": {
			"version": "2.0.2",
			"resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz",
			"integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==",
			"license": "MIT",
			"dependencies": {
				"function-bind": "^1.1.2"
			},
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/helmet": {
			"version": "8.1.0",
			"resolved": "https://registry.npmjs.org/helmet/-/helmet-8.1.0.tgz",
			"integrity": "sha512-jOiHyAZsmnr8LqoPGmCjYAaiuWwjAPLgY8ZX2XrmHawt99/u1y6RgrZMTeoPfpUbV96HOalYgz1qzkRbw54Pmg==",
			"license": "MIT",
			"engines": {
				"node": ">=18.0.0"
			}
		},
		"node_modules/hexoid": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/hexoid/-/hexoid-2.0.0.tgz",
			"integrity": "sha512-qlspKUK7IlSQv2o+5I7yhUd7TxlOG2Vr5LTa3ve2XSNVKAL/n/u/7KLvKmFNimomDIKvZFXWHv0T12mv7rT8Aw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/html-escaper": {
			"version": "2.0.2",
			"resolved": "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz",
			"integrity": "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/http-errors": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz",
			"integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==",
			"license": "MIT",
			"dependencies": {
				"depd": "2.0.0",
				"inherits": "2.0.4",
				"setprototypeof": "1.2.0",
				"statuses": "2.0.1",
				"toidentifier": "1.0.1"
			},
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/https-proxy-agent": {
			"version": "7.0.6",
			"resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz",
			"integrity": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==",
			"license": "MIT",
			"dependencies": {
				"agent-base": "^7.1.2",
				"debug": "4"
			},
			"engines": {
				"node": ">= 14"
			}
		},
		"node_modules/human-id": {
			"version": "4.1.1",
			"resolved": "https://registry.npmjs.org/human-id/-/human-id-4.1.1.tgz",
			"integrity": "sha512-3gKm/gCSUipeLsRYZbbdA1BD83lBoWUkZ7G9VFrhWPAU76KwYo5KR8V28bpoPm/ygy0x5/GCbpRQdY7VLYCoIg==",
			"dev": true,
			"license": "MIT",
			"bin": {
				"human-id": "dist/cli.js"
			}
		},
		"node_modules/human-signals": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/human-signals/-/human-signals-5.0.0.tgz",
			"integrity": "sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==",
			"dev": true,
			"license": "Apache-2.0",
			"engines": {
				"node": ">=16.17.0"
			}
		},
		"node_modules/humanize-ms": {
			"version": "1.2.1",
			"resolved": "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz",
			"integrity": "sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==",
			"license": "MIT",
			"dependencies": {
				"ms": "^2.0.0"
			}
		},
		"node_modules/iconv-lite": {
			"version": "0.4.24",
			"resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz",
			"integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==",
			"license": "MIT",
			"dependencies": {
				"safer-buffer": ">= 2.1.2 < 3"
			},
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/ieee754": {
			"version": "1.2.1",
			"resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz",
			"integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==",
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/feross"
				},
				{
					"type": "patreon",
					"url": "https://www.patreon.com/feross"
				},
				{
					"type": "consulting",
					"url": "https://feross.org/support"
				}
			],
			"license": "BSD-3-Clause"
		},
		"node_modules/ignore": {
			"version": "5.3.2",
			"resolved": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz",
			"integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">= 4"
			}
		},
		"node_modules/import-local": {
			"version": "3.2.0",
			"resolved": "https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz",
			"integrity": "sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"pkg-dir": "^4.2.0",
				"resolve-cwd": "^3.0.0"
			},
			"bin": {
				"import-local-fixture": "fixtures/cli.js"
			},
			"engines": {
				"node": ">=8"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/imurmurhash": {
			"version": "0.1.4",
			"resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz",
			"integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=0.8.19"
			}
		},
		"node_modules/indent-string": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/indent-string/-/indent-string-5.0.0.tgz",
			"integrity": "sha512-m6FAo/spmsW2Ab2fU35JTYwtOKa2yAwXSwgjSv1TJzh4Mh7mC3lzAOVLBprb72XsTrgkEIsl7YrFNAiDiRhIGg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/inflight": {
			"version": "1.0.6",
			"resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz",
			"integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==",
			"deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"once": "^1.3.0",
				"wrappy": "1"
			}
		},
		"node_modules/inherits": {
			"version": "2.0.4",
			"resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz",
			"integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==",
			"license": "ISC"
		},
		"node_modules/ink": {
			"version": "5.2.0",
			"resolved": "https://registry.npmjs.org/ink/-/ink-5.2.0.tgz",
			"integrity": "sha512-gHzSBBvsh/1ZYuGi+aKzU7RwnYIr6PSz56or9T90i4DDS99euhN7nYKOMR3OTev0dKIB6Zod3vSapYzqoilQcg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@alcalzone/ansi-tokenize": "^0.1.3",
				"ansi-escapes": "^7.0.0",
				"ansi-styles": "^6.2.1",
				"auto-bind": "^5.0.1",
				"chalk": "^5.3.0",
				"cli-boxes": "^3.0.0",
				"cli-cursor": "^4.0.0",
				"cli-truncate": "^4.0.0",
				"code-excerpt": "^4.0.0",
				"es-toolkit": "^1.22.0",
				"indent-string": "^5.0.0",
				"is-in-ci": "^1.0.0",
				"patch-console": "^2.0.0",
				"react-reconciler": "^0.29.0",
				"scheduler": "^0.23.0",
				"signal-exit": "^3.0.7",
				"slice-ansi": "^7.1.0",
				"stack-utils": "^2.0.6",
				"string-width": "^7.2.0",
				"type-fest": "^4.27.0",
				"widest-line": "^5.0.0",
				"wrap-ansi": "^9.0.0",
				"ws": "^8.18.0",
				"yoga-layout": "~3.2.1"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/react": ">=18.0.0",
				"react": ">=18.0.0",
				"react-devtools-core": "^4.19.1"
			},
			"peerDependenciesMeta": {
				"@types/react": {
					"optional": true
				},
				"react-devtools-core": {
					"optional": true
				}
			}
		},
		"node_modules/ink/node_modules/ansi-escapes": {
			"version": "7.0.0",
			"resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-7.0.0.tgz",
			"integrity": "sha512-GdYO7a61mR0fOlAsvC9/rIHf7L96sBc6dEWzeOu+KAea5bZyQRPIpojrVoI4AXGJS/ycu/fBTdLrUkA4ODrvjw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"environment": "^1.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/ink/node_modules/ansi-styles": {
			"version": "6.2.1",
			"resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz",
			"integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/chalk/ansi-styles?sponsor=1"
			}
		},
		"node_modules/ink/node_modules/cli-cursor": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-4.0.0.tgz",
			"integrity": "sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"restore-cursor": "^4.0.0"
			},
			"engines": {
				"node": "^12.20.0 || ^14.13.1 || >=16.0.0"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/ink/node_modules/mimic-fn": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz",
			"integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/ink/node_modules/onetime": {
			"version": "5.1.2",
			"resolved": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz",
			"integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"mimic-fn": "^2.1.0"
			},
			"engines": {
				"node": ">=6"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/ink/node_modules/restore-cursor": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-4.0.0.tgz",
			"integrity": "sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"onetime": "^5.1.0",
				"signal-exit": "^3.0.2"
			},
			"engines": {
				"node": "^12.20.0 || ^14.13.1 || >=16.0.0"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/ink/node_modules/signal-exit": {
			"version": "3.0.7",
			"resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz",
			"integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==",
			"dev": true,
			"license": "ISC"
		},
		"node_modules/inquirer": {
			"version": "12.5.0",
			"resolved": "https://registry.npmjs.org/inquirer/-/inquirer-12.5.0.tgz",
			"integrity": "sha512-aiBBq5aKF1k87MTxXDylLfwpRwToShiHrSv4EmB07EYyLgmnjEz5B3rn0aGw1X3JA/64Ngf2T54oGwc+BCsPIQ==",
			"license": "MIT",
			"dependencies": {
				"@inquirer/core": "^10.1.9",
				"@inquirer/prompts": "^7.4.0",
				"@inquirer/type": "^3.0.5",
				"ansi-escapes": "^4.3.2",
				"mute-stream": "^2.0.0",
				"run-async": "^3.0.0",
				"rxjs": "^7.8.2"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"@types/node": ">=18"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				}
			}
		},
		"node_modules/ipaddr.js": {
			"version": "1.9.1",
			"resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz",
			"integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.10"
			}
		},
		"node_modules/is-arrayish": {
			"version": "0.2.1",
			"resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz",
			"integrity": "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/is-core-module": {
			"version": "2.16.1",
			"resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz",
			"integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"hasown": "^2.0.2"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/is-extglob": {
			"version": "2.1.1",
			"resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz",
			"integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/is-fullwidth-code-point": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz",
			"integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/is-generator-fn": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz",
			"integrity": "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/is-glob": {
			"version": "4.0.3",
			"resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz",
			"integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"is-extglob": "^2.1.1"
			},
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/is-in-ci": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/is-in-ci/-/is-in-ci-1.0.0.tgz",
			"integrity": "sha512-eUuAjybVTHMYWm/U+vBO1sY/JOCgoPCXRxzdju0K+K0BiGW0SChEL1MLC0PoCIR1OlPo5YAp8HuQoUlsWEICwg==",
			"dev": true,
			"license": "MIT",
			"bin": {
				"is-in-ci": "cli.js"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/is-interactive": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/is-interactive/-/is-interactive-2.0.0.tgz",
			"integrity": "sha512-qP1vozQRI+BMOPcjFzrjXuQvdak2pHNUMZoeG2eRbiSqyvbEf/wQtEOTOX1guk6E3t36RkaqiSt8A/6YElNxLQ==",
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/is-number": {
			"version": "7.0.0",
			"resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz",
			"integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=0.12.0"
			}
		},
		"node_modules/is-plain-obj": {
			"version": "4.1.0",
			"resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz",
			"integrity": "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==",
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/is-promise": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz",
			"integrity": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==",
			"license": "MIT"
		},
		"node_modules/is-stream": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/is-stream/-/is-stream-3.0.0.tgz",
			"integrity": "sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": "^12.20.0 || ^14.13.1 || >=16.0.0"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/is-subdir": {
			"version": "1.2.0",
			"resolved": "https://registry.npmjs.org/is-subdir/-/is-subdir-1.2.0.tgz",
			"integrity": "sha512-2AT6j+gXe/1ueqbW6fLZJiIw3F8iXGJtt0yDrZaBhAZEG1raiTxKWU+IPqMCzQAXOUCKdA4UDMgacKH25XG2Cw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"better-path-resolve": "1.0.0"
			},
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/is-unicode-supported": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-2.1.0.tgz",
			"integrity": "sha512-mE00Gnza5EEB3Ds0HfMyllZzbBrmLOX3vfWoj9A9PEnTfratQ/BcaJOuMhnkhjXvb2+FkY3VuHqtAGpTPmglFQ==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/is-windows": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz",
			"integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/isexe": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz",
			"integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==",
			"license": "ISC"
		},
		"node_modules/istanbul-lib-coverage": {
			"version": "3.2.2",
			"resolved": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz",
			"integrity": "sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==",
			"dev": true,
			"license": "BSD-3-Clause",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/istanbul-lib-instrument": {
			"version": "6.0.3",
			"resolved": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz",
			"integrity": "sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"@babel/core": "^7.23.9",
				"@babel/parser": "^7.23.9",
				"@istanbuljs/schema": "^0.1.3",
				"istanbul-lib-coverage": "^3.2.0",
				"semver": "^7.5.4"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/istanbul-lib-instrument/node_modules/semver": {
			"version": "7.7.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
			"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
			"dev": true,
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/istanbul-lib-report": {
			"version": "3.0.1",
			"resolved": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz",
			"integrity": "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"istanbul-lib-coverage": "^3.0.0",
				"make-dir": "^4.0.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/istanbul-lib-source-maps": {
			"version": "4.0.1",
			"resolved": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz",
			"integrity": "sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"debug": "^4.1.1",
				"istanbul-lib-coverage": "^3.0.0",
				"source-map": "^0.6.1"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/istanbul-reports": {
			"version": "3.1.7",
			"resolved": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz",
			"integrity": "sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"html-escaper": "^2.0.0",
				"istanbul-lib-report": "^3.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/jest": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest/-/jest-29.7.0.tgz",
			"integrity": "sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/core": "^29.7.0",
				"@jest/types": "^29.6.3",
				"import-local": "^3.0.2",
				"jest-cli": "^29.7.0"
			},
			"bin": {
				"jest": "bin/jest.js"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			},
			"peerDependencies": {
				"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"
			},
			"peerDependenciesMeta": {
				"node-notifier": {
					"optional": true
				}
			}
		},
		"node_modules/jest-changed-files": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz",
			"integrity": "sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"execa": "^5.0.0",
				"jest-util": "^29.7.0",
				"p-limit": "^3.1.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-changed-files/node_modules/execa": {
			"version": "5.1.1",
			"resolved": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz",
			"integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"cross-spawn": "^7.0.3",
				"get-stream": "^6.0.0",
				"human-signals": "^2.1.0",
				"is-stream": "^2.0.0",
				"merge-stream": "^2.0.0",
				"npm-run-path": "^4.0.1",
				"onetime": "^5.1.2",
				"signal-exit": "^3.0.3",
				"strip-final-newline": "^2.0.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sindresorhus/execa?sponsor=1"
			}
		},
		"node_modules/jest-changed-files/node_modules/get-stream": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz",
			"integrity": "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/jest-changed-files/node_modules/human-signals": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz",
			"integrity": "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==",
			"dev": true,
			"license": "Apache-2.0",
			"engines": {
				"node": ">=10.17.0"
			}
		},
		"node_modules/jest-changed-files/node_modules/is-stream": {
			"version": "2.0.1",
			"resolved": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz",
			"integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/jest-changed-files/node_modules/mimic-fn": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz",
			"integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/jest-changed-files/node_modules/npm-run-path": {
			"version": "4.0.1",
			"resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz",
			"integrity": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"path-key": "^3.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/jest-changed-files/node_modules/onetime": {
			"version": "5.1.2",
			"resolved": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz",
			"integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"mimic-fn": "^2.1.0"
			},
			"engines": {
				"node": ">=6"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/jest-changed-files/node_modules/signal-exit": {
			"version": "3.0.7",
			"resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz",
			"integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==",
			"dev": true,
			"license": "ISC"
		},
		"node_modules/jest-changed-files/node_modules/strip-final-newline": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz",
			"integrity": "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/jest-circus": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-circus/-/jest-circus-29.7.0.tgz",
			"integrity": "sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/environment": "^29.7.0",
				"@jest/expect": "^29.7.0",
				"@jest/test-result": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"chalk": "^4.0.0",
				"co": "^4.6.0",
				"dedent": "^1.0.0",
				"is-generator-fn": "^2.0.0",
				"jest-each": "^29.7.0",
				"jest-matcher-utils": "^29.7.0",
				"jest-message-util": "^29.7.0",
				"jest-runtime": "^29.7.0",
				"jest-snapshot": "^29.7.0",
				"jest-util": "^29.7.0",
				"p-limit": "^3.1.0",
				"pretty-format": "^29.7.0",
				"pure-rand": "^6.0.0",
				"slash": "^3.0.0",
				"stack-utils": "^2.0.3"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-circus/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-cli": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-cli/-/jest-cli-29.7.0.tgz",
			"integrity": "sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/core": "^29.7.0",
				"@jest/test-result": "^29.7.0",
				"@jest/types": "^29.6.3",
				"chalk": "^4.0.0",
				"create-jest": "^29.7.0",
				"exit": "^0.1.2",
				"import-local": "^3.0.2",
				"jest-config": "^29.7.0",
				"jest-util": "^29.7.0",
				"jest-validate": "^29.7.0",
				"yargs": "^17.3.1"
			},
			"bin": {
				"jest": "bin/jest.js"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			},
			"peerDependencies": {
				"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"
			},
			"peerDependenciesMeta": {
				"node-notifier": {
					"optional": true
				}
			}
		},
		"node_modules/jest-cli/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-config": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-config/-/jest-config-29.7.0.tgz",
			"integrity": "sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/core": "^7.11.6",
				"@jest/test-sequencer": "^29.7.0",
				"@jest/types": "^29.6.3",
				"babel-jest": "^29.7.0",
				"chalk": "^4.0.0",
				"ci-info": "^3.2.0",
				"deepmerge": "^4.2.2",
				"glob": "^7.1.3",
				"graceful-fs": "^4.2.9",
				"jest-circus": "^29.7.0",
				"jest-environment-node": "^29.7.0",
				"jest-get-type": "^29.6.3",
				"jest-regex-util": "^29.6.3",
				"jest-resolve": "^29.7.0",
				"jest-runner": "^29.7.0",
				"jest-util": "^29.7.0",
				"jest-validate": "^29.7.0",
				"micromatch": "^4.0.4",
				"parse-json": "^5.2.0",
				"pretty-format": "^29.7.0",
				"slash": "^3.0.0",
				"strip-json-comments": "^3.1.1"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			},
			"peerDependencies": {
				"@types/node": "*",
				"ts-node": ">=9.0.0"
			},
			"peerDependenciesMeta": {
				"@types/node": {
					"optional": true
				},
				"ts-node": {
					"optional": true
				}
			}
		},
		"node_modules/jest-config/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-diff": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz",
			"integrity": "sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"chalk": "^4.0.0",
				"diff-sequences": "^29.6.3",
				"jest-get-type": "^29.6.3",
				"pretty-format": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-diff/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-docblock": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz",
			"integrity": "sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"detect-newline": "^3.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-each": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-each/-/jest-each-29.7.0.tgz",
			"integrity": "sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/types": "^29.6.3",
				"chalk": "^4.0.0",
				"jest-get-type": "^29.6.3",
				"jest-util": "^29.7.0",
				"pretty-format": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-each/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-environment-node": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz",
			"integrity": "sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/environment": "^29.7.0",
				"@jest/fake-timers": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"jest-mock": "^29.7.0",
				"jest-util": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-get-type": {
			"version": "29.6.3",
			"resolved": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz",
			"integrity": "sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-haste-map": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz",
			"integrity": "sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/types": "^29.6.3",
				"@types/graceful-fs": "^4.1.3",
				"@types/node": "*",
				"anymatch": "^3.0.3",
				"fb-watchman": "^2.0.0",
				"graceful-fs": "^4.2.9",
				"jest-regex-util": "^29.6.3",
				"jest-util": "^29.7.0",
				"jest-worker": "^29.7.0",
				"micromatch": "^4.0.4",
				"walker": "^1.0.8"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			},
			"optionalDependencies": {
				"fsevents": "^2.3.2"
			}
		},
		"node_modules/jest-leak-detector": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz",
			"integrity": "sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"jest-get-type": "^29.6.3",
				"pretty-format": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-matcher-utils": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz",
			"integrity": "sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"chalk": "^4.0.0",
				"jest-diff": "^29.7.0",
				"jest-get-type": "^29.6.3",
				"pretty-format": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-matcher-utils/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-message-util": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz",
			"integrity": "sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/code-frame": "^7.12.13",
				"@jest/types": "^29.6.3",
				"@types/stack-utils": "^2.0.0",
				"chalk": "^4.0.0",
				"graceful-fs": "^4.2.9",
				"micromatch": "^4.0.4",
				"pretty-format": "^29.7.0",
				"slash": "^3.0.0",
				"stack-utils": "^2.0.3"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-message-util/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-mock": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz",
			"integrity": "sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"jest-util": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-pnp-resolver": {
			"version": "1.2.3",
			"resolved": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz",
			"integrity": "sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			},
			"peerDependencies": {
				"jest-resolve": "*"
			},
			"peerDependenciesMeta": {
				"jest-resolve": {
					"optional": true
				}
			}
		},
		"node_modules/jest-regex-util": {
			"version": "29.6.3",
			"resolved": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz",
			"integrity": "sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-resolve": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-29.7.0.tgz",
			"integrity": "sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"chalk": "^4.0.0",
				"graceful-fs": "^4.2.9",
				"jest-haste-map": "^29.7.0",
				"jest-pnp-resolver": "^1.2.2",
				"jest-util": "^29.7.0",
				"jest-validate": "^29.7.0",
				"resolve": "^1.20.0",
				"resolve.exports": "^2.0.0",
				"slash": "^3.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-resolve-dependencies": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz",
			"integrity": "sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"jest-regex-util": "^29.6.3",
				"jest-snapshot": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-resolve/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-runner": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-runner/-/jest-runner-29.7.0.tgz",
			"integrity": "sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/console": "^29.7.0",
				"@jest/environment": "^29.7.0",
				"@jest/test-result": "^29.7.0",
				"@jest/transform": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"chalk": "^4.0.0",
				"emittery": "^0.13.1",
				"graceful-fs": "^4.2.9",
				"jest-docblock": "^29.7.0",
				"jest-environment-node": "^29.7.0",
				"jest-haste-map": "^29.7.0",
				"jest-leak-detector": "^29.7.0",
				"jest-message-util": "^29.7.0",
				"jest-resolve": "^29.7.0",
				"jest-runtime": "^29.7.0",
				"jest-util": "^29.7.0",
				"jest-watcher": "^29.7.0",
				"jest-worker": "^29.7.0",
				"p-limit": "^3.1.0",
				"source-map-support": "0.5.13"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-runner/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-runtime": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-runtime/-/jest-runtime-29.7.0.tgz",
			"integrity": "sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/environment": "^29.7.0",
				"@jest/fake-timers": "^29.7.0",
				"@jest/globals": "^29.7.0",
				"@jest/source-map": "^29.6.3",
				"@jest/test-result": "^29.7.0",
				"@jest/transform": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"chalk": "^4.0.0",
				"cjs-module-lexer": "^1.0.0",
				"collect-v8-coverage": "^1.0.0",
				"glob": "^7.1.3",
				"graceful-fs": "^4.2.9",
				"jest-haste-map": "^29.7.0",
				"jest-message-util": "^29.7.0",
				"jest-mock": "^29.7.0",
				"jest-regex-util": "^29.6.3",
				"jest-resolve": "^29.7.0",
				"jest-snapshot": "^29.7.0",
				"jest-util": "^29.7.0",
				"slash": "^3.0.0",
				"strip-bom": "^4.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-runtime/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-snapshot": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-29.7.0.tgz",
			"integrity": "sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/core": "^7.11.6",
				"@babel/generator": "^7.7.2",
				"@babel/plugin-syntax-jsx": "^7.7.2",
				"@babel/plugin-syntax-typescript": "^7.7.2",
				"@babel/types": "^7.3.3",
				"@jest/expect-utils": "^29.7.0",
				"@jest/transform": "^29.7.0",
				"@jest/types": "^29.6.3",
				"babel-preset-current-node-syntax": "^1.0.0",
				"chalk": "^4.0.0",
				"expect": "^29.7.0",
				"graceful-fs": "^4.2.9",
				"jest-diff": "^29.7.0",
				"jest-get-type": "^29.6.3",
				"jest-matcher-utils": "^29.7.0",
				"jest-message-util": "^29.7.0",
				"jest-util": "^29.7.0",
				"natural-compare": "^1.4.0",
				"pretty-format": "^29.7.0",
				"semver": "^7.5.3"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-snapshot/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-snapshot/node_modules/semver": {
			"version": "7.7.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
			"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
			"dev": true,
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/jest-util": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz",
			"integrity": "sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"chalk": "^4.0.0",
				"ci-info": "^3.2.0",
				"graceful-fs": "^4.2.9",
				"picomatch": "^2.2.3"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-util/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-validate": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz",
			"integrity": "sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/types": "^29.6.3",
				"camelcase": "^6.2.0",
				"chalk": "^4.0.0",
				"jest-get-type": "^29.6.3",
				"leven": "^3.1.0",
				"pretty-format": "^29.7.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-validate/node_modules/camelcase": {
			"version": "6.3.0",
			"resolved": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz",
			"integrity": "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/jest-validate/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-watcher": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz",
			"integrity": "sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/test-result": "^29.7.0",
				"@jest/types": "^29.6.3",
				"@types/node": "*",
				"ansi-escapes": "^4.2.1",
				"chalk": "^4.0.0",
				"emittery": "^0.13.1",
				"jest-util": "^29.7.0",
				"string-length": "^4.0.1"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-watcher/node_modules/chalk": {
			"version": "4.1.2",
			"resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",
			"integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^4.1.0",
				"supports-color": "^7.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/chalk?sponsor=1"
			}
		},
		"node_modules/jest-worker": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz",
			"integrity": "sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@types/node": "*",
				"jest-util": "^29.7.0",
				"merge-stream": "^2.0.0",
				"supports-color": "^8.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/jest-worker/node_modules/supports-color": {
			"version": "8.1.1",
			"resolved": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz",
			"integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"has-flag": "^4.0.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/supports-color?sponsor=1"
			}
		},
		"node_modules/js-tokens": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz",
			"integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==",
			"license": "MIT"
		},
		"node_modules/js-yaml": {
			"version": "3.14.1",
			"resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz",
			"integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"argparse": "^1.0.7",
				"esprima": "^4.0.0"
			},
			"bin": {
				"js-yaml": "bin/js-yaml.js"
			}
		},
		"node_modules/jsesc": {
			"version": "3.1.0",
			"resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz",
			"integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==",
			"dev": true,
			"license": "MIT",
			"bin": {
				"jsesc": "bin/jsesc"
			},
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/json-bigint": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/json-bigint/-/json-bigint-1.0.0.tgz",
			"integrity": "sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==",
			"license": "MIT",
			"dependencies": {
				"bignumber.js": "^9.0.0"
			}
		},
		"node_modules/json-parse-even-better-errors": {
			"version": "2.3.1",
			"resolved": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz",
			"integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/json-schema": {
			"version": "0.4.0",
			"resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz",
			"integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==",
			"license": "(AFL-2.1 OR BSD-3-Clause)"
		},
		"node_modules/json-schema-traverse": {
			"version": "0.4.1",
			"resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz",
			"integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==",
			"license": "MIT"
		},
		"node_modules/json5": {
			"version": "2.2.3",
			"resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz",
			"integrity": "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==",
			"dev": true,
			"license": "MIT",
			"bin": {
				"json5": "lib/cli.js"
			},
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/jsondiffpatch": {
			"version": "0.6.0",
			"resolved": "https://registry.npmjs.org/jsondiffpatch/-/jsondiffpatch-0.6.0.tgz",
			"integrity": "sha512-3QItJOXp2AP1uv7waBkao5nCvhEv+QmJAd38Ybq7wNI74Q+BBmnLn4EDKz6yI9xGAIQoUF87qHt+kc1IVxB4zQ==",
			"license": "MIT",
			"dependencies": {
				"@types/diff-match-patch": "^1.0.36",
				"chalk": "^5.3.0",
				"diff-match-patch": "^1.0.5"
			},
			"bin": {
				"jsondiffpatch": "bin/jsondiffpatch.js"
			},
			"engines": {
				"node": "^18.0.0 || >=20.0.0"
			}
		},
		"node_modules/jsonfile": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz",
			"integrity": "sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==",
			"dev": true,
			"license": "MIT",
			"optionalDependencies": {
				"graceful-fs": "^4.1.6"
			}
		},
		"node_modules/jsonwebtoken": {
			"version": "9.0.2",
			"resolved": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz",
			"integrity": "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==",
			"license": "MIT",
			"dependencies": {
				"jws": "^3.2.2",
				"lodash.includes": "^4.3.0",
				"lodash.isboolean": "^3.0.3",
				"lodash.isinteger": "^4.0.4",
				"lodash.isnumber": "^3.0.3",
				"lodash.isplainobject": "^4.0.6",
				"lodash.isstring": "^4.0.1",
				"lodash.once": "^4.0.0",
				"ms": "^2.1.1",
				"semver": "^7.5.4"
			},
			"engines": {
				"node": ">=12",
				"npm": ">=6"
			}
		},
		"node_modules/jsonwebtoken/node_modules/semver": {
			"version": "7.7.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
			"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/jwa": {
			"version": "1.4.1",
			"resolved": "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz",
			"integrity": "sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==",
			"license": "MIT",
			"dependencies": {
				"buffer-equal-constant-time": "1.0.1",
				"ecdsa-sig-formatter": "1.0.11",
				"safe-buffer": "^5.0.1"
			}
		},
		"node_modules/jws": {
			"version": "3.2.2",
			"resolved": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz",
			"integrity": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==",
			"license": "MIT",
			"dependencies": {
				"jwa": "^1.4.1",
				"safe-buffer": "^5.0.1"
			}
		},
		"node_modules/kleur": {
			"version": "3.0.3",
			"resolved": "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz",
			"integrity": "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/leven": {
			"version": "3.1.0",
			"resolved": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz",
			"integrity": "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/lines-and-columns": {
			"version": "1.2.4",
			"resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz",
			"integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/locate-path": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz",
			"integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"p-locate": "^4.1.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/lodash.includes": {
			"version": "4.3.0",
			"resolved": "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz",
			"integrity": "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==",
			"license": "MIT"
		},
		"node_modules/lodash.isboolean": {
			"version": "3.0.3",
			"resolved": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz",
			"integrity": "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==",
			"license": "MIT"
		},
		"node_modules/lodash.isinteger": {
			"version": "4.0.4",
			"resolved": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz",
			"integrity": "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==",
			"license": "MIT"
		},
		"node_modules/lodash.isnumber": {
			"version": "3.0.3",
			"resolved": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz",
			"integrity": "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==",
			"license": "MIT"
		},
		"node_modules/lodash.isplainobject": {
			"version": "4.0.6",
			"resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz",
			"integrity": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==",
			"license": "MIT"
		},
		"node_modules/lodash.isstring": {
			"version": "4.0.1",
			"resolved": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz",
			"integrity": "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==",
			"license": "MIT"
		},
		"node_modules/lodash.once": {
			"version": "4.1.1",
			"resolved": "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz",
			"integrity": "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==",
			"license": "MIT"
		},
		"node_modules/lodash.startcase": {
			"version": "4.4.0",
			"resolved": "https://registry.npmjs.org/lodash.startcase/-/lodash.startcase-4.4.0.tgz",
			"integrity": "sha512-+WKqsK294HMSc2jEbNgpHpd0JfIBhp7rEV4aqXWqFr6AlXov+SlcgB1Fv01y2kGe3Gc8nMW7VA0SrGuSkRfIEg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/log-symbols": {
			"version": "6.0.0",
			"resolved": "https://registry.npmjs.org/log-symbols/-/log-symbols-6.0.0.tgz",
			"integrity": "sha512-i24m8rpwhmPIS4zscNzK6MSEhk0DUWa/8iYQWxhffV8jkI4Phvs3F+quL5xvS0gdQR0FyTCMMH33Y78dDTzzIw==",
			"license": "MIT",
			"dependencies": {
				"chalk": "^5.3.0",
				"is-unicode-supported": "^1.3.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/log-symbols/node_modules/is-unicode-supported": {
			"version": "1.3.0",
			"resolved": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz",
			"integrity": "sha512-43r2mRvz+8JRIKnWJ+3j8JtjRKZ6GmjzfaE/qiBJnikNnYv/6bagRJ1kUhNk8R5EX/GkobD+r+sfxCPJsiKBLQ==",
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/loose-envify": {
			"version": "1.4.0",
			"resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz",
			"integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==",
			"license": "MIT",
			"dependencies": {
				"js-tokens": "^3.0.0 || ^4.0.0"
			},
			"bin": {
				"loose-envify": "cli.js"
			}
		},
		"node_modules/lru-cache": {
			"version": "10.4.3",
			"resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz",
			"integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==",
			"license": "ISC"
		},
		"node_modules/make-dir": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz",
			"integrity": "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"semver": "^7.5.3"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/make-dir/node_modules/semver": {
			"version": "7.7.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
			"integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
			"dev": true,
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/makeerror": {
			"version": "1.0.12",
			"resolved": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz",
			"integrity": "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==",
			"dev": true,
			"license": "BSD-3-Clause",
			"dependencies": {
				"tmpl": "1.0.5"
			}
		},
		"node_modules/math-intrinsics": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz",
			"integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.4"
			}
		},
		"node_modules/mcp-proxy": {
			"version": "3.3.0",
			"resolved": "https://registry.npmjs.org/mcp-proxy/-/mcp-proxy-3.3.0.tgz",
			"integrity": "sha512-xyFKQEZ64HC7lxScBHjb5fxiPoyJjjkPhwH5hWUT0oL/ttCpMGZDJrYZRGFKVJiLLkrZPAkHnMGkI+WMlyD/cg==",
			"license": "MIT",
			"dependencies": {
				"@modelcontextprotocol/sdk": "^1.11.4",
				"eventsource": "^4.0.0",
				"yargs": "^17.7.2"
			},
			"bin": {
				"mcp-proxy": "dist/bin/mcp-proxy.js"
			}
		},
		"node_modules/mcp-proxy/node_modules/eventsource": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/eventsource/-/eventsource-4.0.0.tgz",
			"integrity": "sha512-fvIkb9qZzdMxgZrEQDyll+9oJsyaVvY92I2Re+qK0qEJ+w5s0X3dtz+M0VAPOjP1gtU3iqWyjQ0G3nvd5CLZ2g==",
			"license": "MIT",
			"dependencies": {
				"eventsource-parser": "^3.0.1"
			},
			"engines": {
				"node": ">=20.0.0"
			}
		},
		"node_modules/media-typer": {
			"version": "0.3.0",
			"resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz",
			"integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/merge-descriptors": {
			"version": "1.0.3",
			"resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz",
			"integrity": "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==",
			"license": "MIT",
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/merge-stream": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz",
			"integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/merge2": {
			"version": "1.4.1",
			"resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz",
			"integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">= 8"
			}
		},
		"node_modules/methods": {
			"version": "1.1.2",
			"resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz",
			"integrity": "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/micromatch": {
			"version": "4.0.8",
			"resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz",
			"integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"braces": "^3.0.3",
				"picomatch": "^2.3.1"
			},
			"engines": {
				"node": ">=8.6"
			}
		},
		"node_modules/mime": {
			"version": "2.6.0",
			"resolved": "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz",
			"integrity": "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==",
			"dev": true,
			"license": "MIT",
			"bin": {
				"mime": "cli.js"
			},
			"engines": {
				"node": ">=4.0.0"
			}
		},
		"node_modules/mime-db": {
			"version": "1.52.0",
			"resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz",
			"integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/mime-types": {
			"version": "2.1.35",
			"resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz",
			"integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==",
			"license": "MIT",
			"dependencies": {
				"mime-db": "1.52.0"
			},
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/mimic-fn": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz",
			"integrity": "sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/mimic-function": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/mimic-function/-/mimic-function-5.0.1.tgz",
			"integrity": "sha512-VP79XUPxV2CigYP3jWwAUFSku2aKqBH7uTAapFWCBqutsbmDo96KY5o8uh6U+/YSIn5OxJnXp73beVkpqMIGhA==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/minimatch": {
			"version": "3.1.2",
			"resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",
			"integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"brace-expansion": "^1.1.7"
			},
			"engines": {
				"node": "*"
			}
		},
		"node_modules/mock-fs": {
			"version": "5.5.0",
			"resolved": "https://registry.npmjs.org/mock-fs/-/mock-fs-5.5.0.tgz",
			"integrity": "sha512-d/P1M/RacgM3dB0sJ8rjeRNXxtapkPCUnMGmIN0ixJ16F/E4GUZCvWcSGfWGz8eaXYvn1s9baUwNjI4LOPEjiA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12.0.0"
			}
		},
		"node_modules/mri": {
			"version": "1.2.0",
			"resolved": "https://registry.npmjs.org/mri/-/mri-1.2.0.tgz",
			"integrity": "sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/ms": {
			"version": "2.1.3",
			"resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz",
			"integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==",
			"license": "MIT"
		},
		"node_modules/mute-stream": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-2.0.0.tgz",
			"integrity": "sha512-WWdIxpyjEn+FhQJQQv9aQAYlHoNVdzIzUySNV1gHUPDSdZJ3yZn7pAAbQcV7B56Mvu881q9FZV+0Vx2xC44VWA==",
			"license": "ISC",
			"engines": {
				"node": "^18.17.0 || >=20.5.0"
			}
		},
		"node_modules/nanoid": {
			"version": "3.3.11",
			"resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz",
			"integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==",
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/ai"
				}
			],
			"license": "MIT",
			"bin": {
				"nanoid": "bin/nanoid.cjs"
			},
			"engines": {
				"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"
			}
		},
		"node_modules/natural-compare": {
			"version": "1.4.0",
			"resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz",
			"integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/negotiator": {
			"version": "0.6.3",
			"resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz",
			"integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/node-domexception": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz",
			"integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==",
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/jimmywarting"
				},
				{
					"type": "github",
					"url": "https://paypal.me/jimmywarting"
				}
			],
			"license": "MIT",
			"engines": {
				"node": ">=10.5.0"
			}
		},
		"node_modules/node-int64": {
			"version": "0.4.0",
			"resolved": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz",
			"integrity": "sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/node-releases": {
			"version": "2.0.19",
			"resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz",
			"integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/normalize-path": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz",
			"integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/npm-run-path": {
			"version": "5.3.0",
			"resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.3.0.tgz",
			"integrity": "sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"path-key": "^4.0.0"
			},
			"engines": {
				"node": "^12.20.0 || ^14.13.1 || >=16.0.0"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/npm-run-path/node_modules/path-key": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz",
			"integrity": "sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/object-assign": {
			"version": "4.1.1",
			"resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz",
			"integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==",
			"license": "MIT",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/object-inspect": {
			"version": "1.13.4",
			"resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz",
			"integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/ollama-ai-provider": {
			"version": "1.2.0",
			"resolved": "https://registry.npmjs.org/ollama-ai-provider/-/ollama-ai-provider-1.2.0.tgz",
			"integrity": "sha512-jTNFruwe3O/ruJeppI/quoOUxG7NA6blG3ZyQj3lei4+NnJo7bi3eIRWqlVpRlu/mbzbFXeJSBuYQWF6pzGKww==",
			"license": "Apache-2.0",
			"dependencies": {
				"@ai-sdk/provider": "^1.0.0",
				"@ai-sdk/provider-utils": "^2.0.0",
				"partial-json": "0.1.7"
			},
			"engines": {
				"node": ">=18"
			},
			"peerDependencies": {
				"zod": "^3.0.0"
			},
			"peerDependenciesMeta": {
				"zod": {
					"optional": true
				}
			}
		},
		"node_modules/on-finished": {
			"version": "2.4.1",
			"resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz",
			"integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==",
			"license": "MIT",
			"dependencies": {
				"ee-first": "1.1.1"
			},
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/once": {
			"version": "1.4.0",
			"resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz",
			"integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==",
			"license": "ISC",
			"dependencies": {
				"wrappy": "1"
			}
		},
		"node_modules/onetime": {
			"version": "7.0.0",
			"resolved": "https://registry.npmjs.org/onetime/-/onetime-7.0.0.tgz",
			"integrity": "sha512-VXJjc87FScF88uafS3JllDgvAm+c/Slfz06lorj2uAY34rlUu0Nt+v8wreiImcrgAjjIHp1rXpTDlLOGw29WwQ==",
			"license": "MIT",
			"dependencies": {
				"mimic-function": "^5.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/openai": {
			"version": "4.89.0",
			"resolved": "https://registry.npmjs.org/openai/-/openai-4.89.0.tgz",
			"integrity": "sha512-XNI0q2l8/Os6jmojxaID5EhyQjxZgzR2gWcpEjYWK5hGKwE7AcifxEY7UNwFDDHJQXqeiosQ0CJwQN+rvnwdjA==",
			"license": "Apache-2.0",
			"dependencies": {
				"@types/node": "^18.11.18",
				"@types/node-fetch": "^2.6.4",
				"abort-controller": "^3.0.0",
				"agentkeepalive": "^4.2.1",
				"form-data-encoder": "1.7.2",
				"formdata-node": "^4.3.2",
				"node-fetch": "^2.6.7"
			},
			"bin": {
				"openai": "bin/cli"
			},
			"peerDependencies": {
				"ws": "^8.18.0",
				"zod": "^3.23.8"
			},
			"peerDependenciesMeta": {
				"ws": {
					"optional": true
				},
				"zod": {
					"optional": true
				}
			}
		},
		"node_modules/openai/node_modules/node-fetch": {
			"version": "2.7.0",
			"resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz",
			"integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==",
			"license": "MIT",
			"dependencies": {
				"whatwg-url": "^5.0.0"
			},
			"engines": {
				"node": "4.x || >=6.0.0"
			},
			"peerDependencies": {
				"encoding": "^0.1.0"
			},
			"peerDependenciesMeta": {
				"encoding": {
					"optional": true
				}
			}
		},
		"node_modules/ora": {
			"version": "8.2.0",
			"resolved": "https://registry.npmjs.org/ora/-/ora-8.2.0.tgz",
			"integrity": "sha512-weP+BZ8MVNnlCm8c0Qdc1WSWq4Qn7I+9CJGm7Qali6g44e/PUzbjNqJX5NJ9ljlNMosfJvg1fKEGILklK9cwnw==",
			"license": "MIT",
			"dependencies": {
				"chalk": "^5.3.0",
				"cli-cursor": "^5.0.0",
				"cli-spinners": "^2.9.2",
				"is-interactive": "^2.0.0",
				"is-unicode-supported": "^2.0.0",
				"log-symbols": "^6.0.0",
				"stdin-discarder": "^0.2.2",
				"string-width": "^7.2.0",
				"strip-ansi": "^7.1.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/os-tmpdir": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz",
			"integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==",
			"license": "MIT",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/outdent": {
			"version": "0.5.0",
			"resolved": "https://registry.npmjs.org/outdent/-/outdent-0.5.0.tgz",
			"integrity": "sha512-/jHxFIzoMXdqPzTaCpFzAAWhpkSjZPF4Vsn6jAfNpmbH/ymsmd7Qc6VE9BGn0L6YMj6uwpQLxCECpus4ukKS9Q==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/p-filter": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/p-filter/-/p-filter-2.1.0.tgz",
			"integrity": "sha512-ZBxxZ5sL2HghephhpGAQdoskxplTwr7ICaehZwLIlfL6acuVgZPm8yBNuRAFBGEqtD/hmUeq9eqLg2ys9Xr/yw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"p-map": "^2.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/p-limit": {
			"version": "3.1.0",
			"resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz",
			"integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"yocto-queue": "^0.1.0"
			},
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/p-locate": {
			"version": "4.1.0",
			"resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz",
			"integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"p-limit": "^2.2.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/p-locate/node_modules/p-limit": {
			"version": "2.3.0",
			"resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz",
			"integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"p-try": "^2.0.0"
			},
			"engines": {
				"node": ">=6"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/p-map": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/p-map/-/p-map-2.1.0.tgz",
			"integrity": "sha512-y3b8Kpd8OAN444hxfBbFfj1FY/RjtTd8tzYwhUqNYXx0fXx2iX4maP4Qr6qhIKbQXI02wTLAda4fYUbDagTUFw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/p-try": {
			"version": "2.2.0",
			"resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz",
			"integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/package-manager-detector": {
			"version": "0.2.11",
			"resolved": "https://registry.npmjs.org/package-manager-detector/-/package-manager-detector-0.2.11.tgz",
			"integrity": "sha512-BEnLolu+yuz22S56CU1SUKq3XC3PkwD5wv4ikR4MfGvnRVcmzXR9DwSlW2fEamyTPyXHomBJRzgapeuBvRNzJQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"quansync": "^0.2.7"
			}
		},
		"node_modules/parse-json": {
			"version": "5.2.0",
			"resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz",
			"integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@babel/code-frame": "^7.0.0",
				"error-ex": "^1.3.1",
				"json-parse-even-better-errors": "^2.3.0",
				"lines-and-columns": "^1.1.6"
			},
			"engines": {
				"node": ">=8"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/parse-ms": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/parse-ms/-/parse-ms-4.0.0.tgz",
			"integrity": "sha512-TXfryirbmq34y8QBwgqCVLi+8oA3oWx2eAnSn62ITyEhEYaWRlVZ2DvMM9eZbMs/RfxPu/PK/aBLyGj4IrqMHw==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/parseurl": {
			"version": "1.3.3",
			"resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz",
			"integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/partial-json": {
			"version": "0.1.7",
			"resolved": "https://registry.npmjs.org/partial-json/-/partial-json-0.1.7.tgz",
			"integrity": "sha512-Njv/59hHaokb/hRUjce3Hdv12wd60MtM9Z5Olmn+nehe0QDAsRtRbJPvJ0Z91TusF0SuZRIvnM+S4l6EIP8leA==",
			"license": "MIT"
		},
		"node_modules/patch-console": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/patch-console/-/patch-console-2.0.0.tgz",
			"integrity": "sha512-0YNdUceMdaQwoKce1gatDScmMo5pu/tfABfnzEqeG0gtTmd7mh/WcwgUjtAeOU7N8nFFlbQBnFK2gXW5fGvmMA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": "^12.20.0 || ^14.13.1 || >=16.0.0"
			}
		},
		"node_modules/path-exists": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz",
			"integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/path-is-absolute": {
			"version": "1.0.1",
			"resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz",
			"integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/path-key": {
			"version": "3.1.1",
			"resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz",
			"integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/path-parse": {
			"version": "1.0.7",
			"resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz",
			"integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/path-to-regexp": {
			"version": "0.1.12",
			"resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.12.tgz",
			"integrity": "sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==",
			"license": "MIT"
		},
		"node_modules/path-type": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz",
			"integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/peek-readable": {
			"version": "7.0.0",
			"resolved": "https://registry.npmjs.org/peek-readable/-/peek-readable-7.0.0.tgz",
			"integrity": "sha512-nri2TO5JE3/mRryik9LlHFT53cgHfRK0Lt0BAZQXku/AW3E6XLt2GaY8siWi7dvW/m1z0ecn+J+bpDa9ZN3IsQ==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"type": "github",
				"url": "https://github.com/sponsors/Borewit"
			}
		},
		"node_modules/picocolors": {
			"version": "1.1.1",
			"resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz",
			"integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==",
			"dev": true,
			"license": "ISC"
		},
		"node_modules/picomatch": {
			"version": "2.3.1",
			"resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz",
			"integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8.6"
			},
			"funding": {
				"url": "https://github.com/sponsors/jonschlinkert"
			}
		},
		"node_modules/pify": {
			"version": "4.0.1",
			"resolved": "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz",
			"integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/pirates": {
			"version": "4.0.6",
			"resolved": "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz",
			"integrity": "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">= 6"
			}
		},
		"node_modules/pkce-challenge": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/pkce-challenge/-/pkce-challenge-5.0.0.tgz",
			"integrity": "sha512-ueGLflrrnvwB3xuo/uGob5pd5FN7l0MsLf0Z87o/UQmRtwjvfylfc9MurIxRAWywCYTgrvpXBcqjV4OfCYGCIQ==",
			"license": "MIT",
			"engines": {
				"node": ">=16.20.0"
			}
		},
		"node_modules/pkg-dir": {
			"version": "4.2.0",
			"resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz",
			"integrity": "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"find-up": "^4.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/prettier": {
			"version": "3.5.3",
			"resolved": "https://registry.npmjs.org/prettier/-/prettier-3.5.3.tgz",
			"integrity": "sha512-QQtaxnoDJeAkDvDKWCLiwIXkTgRhwYDEQCghU9Z6q03iyek/rxRh/2lC3HB7P8sWT2xC/y5JDctPLBIGzHKbhw==",
			"dev": true,
			"license": "MIT",
			"bin": {
				"prettier": "bin/prettier.cjs"
			},
			"engines": {
				"node": ">=14"
			},
			"funding": {
				"url": "https://github.com/prettier/prettier?sponsor=1"
			}
		},
		"node_modules/pretty-format": {
			"version": "29.7.0",
			"resolved": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz",
			"integrity": "sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"@jest/schemas": "^29.6.3",
				"ansi-styles": "^5.0.0",
				"react-is": "^18.0.0"
			},
			"engines": {
				"node": "^14.15.0 || ^16.10.0 || >=18.0.0"
			}
		},
		"node_modules/pretty-format/node_modules/ansi-styles": {
			"version": "5.2.0",
			"resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz",
			"integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/chalk/ansi-styles?sponsor=1"
			}
		},
		"node_modules/pretty-ms": {
			"version": "9.2.0",
			"resolved": "https://registry.npmjs.org/pretty-ms/-/pretty-ms-9.2.0.tgz",
			"integrity": "sha512-4yf0QO/sllf/1zbZWYnvWw3NxCQwLXKzIj0G849LSufP15BXKM0rbD2Z3wVnkMfjdn/CB0Dpp444gYAACdsplg==",
			"license": "MIT",
			"dependencies": {
				"parse-ms": "^4.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/prompts": {
			"version": "2.4.2",
			"resolved": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz",
			"integrity": "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"kleur": "^3.0.3",
				"sisteransi": "^1.0.5"
			},
			"engines": {
				"node": ">= 6"
			}
		},
		"node_modules/proxy-addr": {
			"version": "2.0.7",
			"resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz",
			"integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==",
			"license": "MIT",
			"dependencies": {
				"forwarded": "0.2.0",
				"ipaddr.js": "1.9.1"
			},
			"engines": {
				"node": ">= 0.10"
			}
		},
		"node_modules/punycode": {
			"version": "2.3.1",
			"resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz",
			"integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==",
			"license": "MIT",
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/pure-rand": {
			"version": "6.1.0",
			"resolved": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.1.0.tgz",
			"integrity": "sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==",
			"dev": true,
			"funding": [
				{
					"type": "individual",
					"url": "https://github.com/sponsors/dubzzz"
				},
				{
					"type": "opencollective",
					"url": "https://opencollective.com/fast-check"
				}
			],
			"license": "MIT"
		},
		"node_modules/qs": {
			"version": "6.14.0",
			"resolved": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz",
			"integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==",
			"license": "BSD-3-Clause",
			"dependencies": {
				"side-channel": "^1.1.0"
			},
			"engines": {
				"node": ">=0.6"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/quansync": {
			"version": "0.2.10",
			"resolved": "https://registry.npmjs.org/quansync/-/quansync-0.2.10.tgz",
			"integrity": "sha512-t41VRkMYbkHyCYmOvx/6URnN80H7k4X0lLdBMGsz+maAwrJQYB1djpV6vHrQIBE0WBSGqhtEHrK9U3DWWH8v7A==",
			"dev": true,
			"funding": [
				{
					"type": "individual",
					"url": "https://github.com/sponsors/antfu"
				},
				{
					"type": "individual",
					"url": "https://github.com/sponsors/sxzz"
				}
			],
			"license": "MIT"
		},
		"node_modules/queue-microtask": {
			"version": "1.2.3",
			"resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz",
			"integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==",
			"dev": true,
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/feross"
				},
				{
					"type": "patreon",
					"url": "https://www.patreon.com/feross"
				},
				{
					"type": "consulting",
					"url": "https://feross.org/support"
				}
			],
			"license": "MIT"
		},
		"node_modules/range-parser": {
			"version": "1.2.1",
			"resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz",
			"integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/raw-body": {
			"version": "2.5.2",
			"resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz",
			"integrity": "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==",
			"license": "MIT",
			"dependencies": {
				"bytes": "3.1.2",
				"http-errors": "2.0.0",
				"iconv-lite": "0.4.24",
				"unpipe": "1.0.0"
			},
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/react": {
			"version": "18.3.1",
			"resolved": "https://registry.npmjs.org/react/-/react-18.3.1.tgz",
			"integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==",
			"license": "MIT",
			"dependencies": {
				"loose-envify": "^1.1.0"
			},
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/react-is": {
			"version": "18.3.1",
			"resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz",
			"integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/react-reconciler": {
			"version": "0.29.2",
			"resolved": "https://registry.npmjs.org/react-reconciler/-/react-reconciler-0.29.2.tgz",
			"integrity": "sha512-zZQqIiYgDCTP/f1N/mAR10nJGrPD2ZR+jDSEsKWJHYC7Cm2wodlwbR3upZRdC3cjIjSlTLNVyO7Iu0Yy7t2AYg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"loose-envify": "^1.1.0",
				"scheduler": "^0.23.2"
			},
			"engines": {
				"node": ">=0.10.0"
			},
			"peerDependencies": {
				"react": "^18.3.1"
			}
		},
		"node_modules/read-yaml-file": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/read-yaml-file/-/read-yaml-file-1.1.0.tgz",
			"integrity": "sha512-VIMnQi/Z4HT2Fxuwg5KrY174U1VdUIASQVWXXyqtNRtxSr9IYkn1rsI6Tb6HsrHCmB7gVpNwX6JxPTHcH6IoTA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"graceful-fs": "^4.1.5",
				"js-yaml": "^3.6.1",
				"pify": "^4.0.1",
				"strip-bom": "^3.0.0"
			},
			"engines": {
				"node": ">=6"
			}
		},
		"node_modules/read-yaml-file/node_modules/strip-bom": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz",
			"integrity": "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/regenerator-runtime": {
			"version": "0.14.1",
			"resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz",
			"integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/require-directory": {
			"version": "2.1.1",
			"resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz",
			"integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==",
			"license": "MIT",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/resolve": {
			"version": "1.22.10",
			"resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz",
			"integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"is-core-module": "^2.16.0",
				"path-parse": "^1.0.7",
				"supports-preserve-symlinks-flag": "^1.0.0"
			},
			"bin": {
				"resolve": "bin/resolve"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/resolve-cwd": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz",
			"integrity": "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"resolve-from": "^5.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/resolve-from": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz",
			"integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/resolve-pkg-maps": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz",
			"integrity": "sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==",
			"dev": true,
			"license": "MIT",
			"funding": {
				"url": "https://github.com/privatenumber/resolve-pkg-maps?sponsor=1"
			}
		},
		"node_modules/resolve.exports": {
			"version": "2.0.3",
			"resolved": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz",
			"integrity": "sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/restore-cursor": {
			"version": "5.1.0",
			"resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-5.1.0.tgz",
			"integrity": "sha512-oMA2dcrw6u0YfxJQXm342bFKX/E4sG9rbTzO9ptUcR/e8A33cHuvStiYOwH7fszkZlZ1z/ta9AAoPk2F4qIOHA==",
			"license": "MIT",
			"dependencies": {
				"onetime": "^7.0.0",
				"signal-exit": "^4.1.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/reusify": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz",
			"integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"iojs": ">=1.0.0",
				"node": ">=0.10.0"
			}
		},
		"node_modules/router": {
			"version": "2.2.0",
			"resolved": "https://registry.npmjs.org/router/-/router-2.2.0.tgz",
			"integrity": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==",
			"license": "MIT",
			"dependencies": {
				"debug": "^4.4.0",
				"depd": "^2.0.0",
				"is-promise": "^4.0.0",
				"parseurl": "^1.3.3",
				"path-to-regexp": "^8.0.0"
			},
			"engines": {
				"node": ">= 18"
			}
		},
		"node_modules/router/node_modules/path-to-regexp": {
			"version": "8.2.0",
			"resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz",
			"integrity": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==",
			"license": "MIT",
			"engines": {
				"node": ">=16"
			}
		},
		"node_modules/run-async": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/run-async/-/run-async-3.0.0.tgz",
			"integrity": "sha512-540WwVDOMxA6dN6We19EcT9sc3hkXPw5mzRNGM3FkdN/vtE9NFvj5lFAPNwUDmJjXidm3v7TC1cTE7t17Ulm1Q==",
			"license": "MIT",
			"engines": {
				"node": ">=0.12.0"
			}
		},
		"node_modules/run-parallel": {
			"version": "1.2.0",
			"resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz",
			"integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==",
			"dev": true,
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/feross"
				},
				{
					"type": "patreon",
					"url": "https://www.patreon.com/feross"
				},
				{
					"type": "consulting",
					"url": "https://feross.org/support"
				}
			],
			"license": "MIT",
			"dependencies": {
				"queue-microtask": "^1.2.2"
			}
		},
		"node_modules/rxjs": {
			"version": "7.8.2",
			"resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz",
			"integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==",
			"license": "Apache-2.0",
			"dependencies": {
				"tslib": "^2.1.0"
			}
		},
		"node_modules/safe-buffer": {
			"version": "5.2.1",
			"resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz",
			"integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==",
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/feross"
				},
				{
					"type": "patreon",
					"url": "https://www.patreon.com/feross"
				},
				{
					"type": "consulting",
					"url": "https://feross.org/support"
				}
			],
			"license": "MIT"
		},
		"node_modules/safer-buffer": {
			"version": "2.1.2",
			"resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz",
			"integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==",
			"license": "MIT"
		},
		"node_modules/scheduler": {
			"version": "0.23.2",
			"resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz",
			"integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"loose-envify": "^1.1.0"
			}
		},
		"node_modules/secure-json-parse": {
			"version": "2.7.0",
			"resolved": "https://registry.npmjs.org/secure-json-parse/-/secure-json-parse-2.7.0.tgz",
			"integrity": "sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==",
			"license": "BSD-3-Clause"
		},
		"node_modules/semver": {
			"version": "6.3.1",
			"resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz",
			"integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==",
			"dev": true,
			"license": "ISC",
			"bin": {
				"semver": "bin/semver.js"
			}
		},
		"node_modules/send": {
			"version": "0.19.0",
			"resolved": "https://registry.npmjs.org/send/-/send-0.19.0.tgz",
			"integrity": "sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==",
			"license": "MIT",
			"dependencies": {
				"debug": "2.6.9",
				"depd": "2.0.0",
				"destroy": "1.2.0",
				"encodeurl": "~1.0.2",
				"escape-html": "~1.0.3",
				"etag": "~1.8.1",
				"fresh": "0.5.2",
				"http-errors": "2.0.0",
				"mime": "1.6.0",
				"ms": "2.1.3",
				"on-finished": "2.4.1",
				"range-parser": "~1.2.1",
				"statuses": "2.0.1"
			},
			"engines": {
				"node": ">= 0.8.0"
			}
		},
		"node_modules/send/node_modules/debug": {
			"version": "2.6.9",
			"resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz",
			"integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==",
			"license": "MIT",
			"dependencies": {
				"ms": "2.0.0"
			}
		},
		"node_modules/send/node_modules/debug/node_modules/ms": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz",
			"integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==",
			"license": "MIT"
		},
		"node_modules/send/node_modules/encodeurl": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz",
			"integrity": "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/send/node_modules/mime": {
			"version": "1.6.0",
			"resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz",
			"integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==",
			"license": "MIT",
			"bin": {
				"mime": "cli.js"
			},
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/serve-static": {
			"version": "1.16.2",
			"resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz",
			"integrity": "sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==",
			"license": "MIT",
			"dependencies": {
				"encodeurl": "~2.0.0",
				"escape-html": "~1.0.3",
				"parseurl": "~1.3.3",
				"send": "0.19.0"
			},
			"engines": {
				"node": ">= 0.8.0"
			}
		},
		"node_modules/setprototypeof": {
			"version": "1.2.0",
			"resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz",
			"integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==",
			"license": "ISC"
		},
		"node_modules/shebang-command": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz",
			"integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==",
			"license": "MIT",
			"dependencies": {
				"shebang-regex": "^3.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/shebang-regex": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz",
			"integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/side-channel": {
			"version": "1.1.0",
			"resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz",
			"integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==",
			"license": "MIT",
			"dependencies": {
				"es-errors": "^1.3.0",
				"object-inspect": "^1.13.3",
				"side-channel-list": "^1.0.0",
				"side-channel-map": "^1.0.1",
				"side-channel-weakmap": "^1.0.2"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/side-channel-list": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz",
			"integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==",
			"license": "MIT",
			"dependencies": {
				"es-errors": "^1.3.0",
				"object-inspect": "^1.13.3"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/side-channel-map": {
			"version": "1.0.1",
			"resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz",
			"integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==",
			"license": "MIT",
			"dependencies": {
				"call-bound": "^1.0.2",
				"es-errors": "^1.3.0",
				"get-intrinsic": "^1.2.5",
				"object-inspect": "^1.13.3"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/side-channel-weakmap": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz",
			"integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==",
			"license": "MIT",
			"dependencies": {
				"call-bound": "^1.0.2",
				"es-errors": "^1.3.0",
				"get-intrinsic": "^1.2.5",
				"object-inspect": "^1.13.3",
				"side-channel-map": "^1.0.1"
			},
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/signal-exit": {
			"version": "4.1.0",
			"resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz",
			"integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==",
			"license": "ISC",
			"engines": {
				"node": ">=14"
			},
			"funding": {
				"url": "https://github.com/sponsors/isaacs"
			}
		},
		"node_modules/sisteransi": {
			"version": "1.0.5",
			"resolved": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz",
			"integrity": "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/slash": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz",
			"integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/slice-ansi": {
			"version": "7.1.0",
			"resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-7.1.0.tgz",
			"integrity": "sha512-bSiSngZ/jWeX93BqeIAbImyTbEihizcwNjFoRUIY/T1wWQsfsm2Vw1agPKylXvQTU7iASGdHhyqRlqQzfz+Htg==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^6.2.1",
				"is-fullwidth-code-point": "^5.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/chalk/slice-ansi?sponsor=1"
			}
		},
		"node_modules/slice-ansi/node_modules/ansi-styles": {
			"version": "6.2.1",
			"resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz",
			"integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/chalk/ansi-styles?sponsor=1"
			}
		},
		"node_modules/slice-ansi/node_modules/is-fullwidth-code-point": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-5.0.0.tgz",
			"integrity": "sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"get-east-asian-width": "^1.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/source-map": {
			"version": "0.6.1",
			"resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz",
			"integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==",
			"dev": true,
			"license": "BSD-3-Clause",
			"engines": {
				"node": ">=0.10.0"
			}
		},
		"node_modules/source-map-support": {
			"version": "0.5.13",
			"resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz",
			"integrity": "sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"buffer-from": "^1.0.0",
				"source-map": "^0.6.0"
			}
		},
		"node_modules/spawndamnit": {
			"version": "3.0.1",
			"resolved": "https://registry.npmjs.org/spawndamnit/-/spawndamnit-3.0.1.tgz",
			"integrity": "sha512-MmnduQUuHCoFckZoWnXsTg7JaiLBJrKFj9UI2MbRPGaJeVpsLcVBu6P/IGZovziM/YBsellCmsprgNA+w0CzVg==",
			"dev": true,
			"license": "SEE LICENSE IN LICENSE",
			"dependencies": {
				"cross-spawn": "^7.0.5",
				"signal-exit": "^4.0.1"
			}
		},
		"node_modules/sprintf-js": {
			"version": "1.0.3",
			"resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz",
			"integrity": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==",
			"dev": true,
			"license": "BSD-3-Clause"
		},
		"node_modules/stack-utils": {
			"version": "2.0.6",
			"resolved": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz",
			"integrity": "sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"escape-string-regexp": "^2.0.0"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/statuses": {
			"version": "2.0.1",
			"resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz",
			"integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/stdin-discarder": {
			"version": "0.2.2",
			"resolved": "https://registry.npmjs.org/stdin-discarder/-/stdin-discarder-0.2.2.tgz",
			"integrity": "sha512-UhDfHmA92YAlNnCfhmq0VeNL5bDbiZGg7sZ2IvPsXubGkiNa9EC+tUTsjBRsYUAz87btI6/1wf4XoVvQ3uRnmQ==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/strict-event-emitter-types": {
			"version": "2.0.0",
			"resolved": "https://registry.npmjs.org/strict-event-emitter-types/-/strict-event-emitter-types-2.0.0.tgz",
			"integrity": "sha512-Nk/brWYpD85WlOgzw5h173aci0Teyv8YdIAEtV+N88nDB0dLlazZyJMIsN6eo1/AR61l+p6CJTG1JIyFaoNEEA==",
			"license": "ISC"
		},
		"node_modules/string-length": {
			"version": "4.0.2",
			"resolved": "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz",
			"integrity": "sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"char-regex": "^1.0.2",
				"strip-ansi": "^6.0.0"
			},
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/string-length/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/string-length/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/string-width": {
			"version": "7.2.0",
			"resolved": "https://registry.npmjs.org/string-width/-/string-width-7.2.0.tgz",
			"integrity": "sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==",
			"license": "MIT",
			"dependencies": {
				"emoji-regex": "^10.3.0",
				"get-east-asian-width": "^1.0.0",
				"strip-ansi": "^7.1.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/strip-ansi": {
			"version": "7.1.0",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz",
			"integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^6.0.1"
			},
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/chalk/strip-ansi?sponsor=1"
			}
		},
		"node_modules/strip-bom": {
			"version": "4.0.0",
			"resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz",
			"integrity": "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/strip-final-newline": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-3.0.0.tgz",
			"integrity": "sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/strip-json-comments": {
			"version": "3.1.1",
			"resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz",
			"integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/strnum": {
			"version": "1.1.2",
			"resolved": "https://registry.npmjs.org/strnum/-/strnum-1.1.2.tgz",
			"integrity": "sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==",
			"funding": [
				{
					"type": "github",
					"url": "https://github.com/sponsors/NaturalIntelligence"
				}
			],
			"license": "MIT"
		},
		"node_modules/strtok3": {
			"version": "10.2.2",
			"resolved": "https://registry.npmjs.org/strtok3/-/strtok3-10.2.2.tgz",
			"integrity": "sha512-Xt18+h4s7Z8xyZ0tmBoRmzxcop97R4BAh+dXouUDCYn+Em+1P3qpkUfI5ueWLT8ynC5hZ+q4iPEmGG1urvQGBg==",
			"license": "MIT",
			"dependencies": {
				"@tokenizer/token": "^0.3.0",
				"peek-readable": "^7.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"type": "github",
				"url": "https://github.com/sponsors/Borewit"
			}
		},
		"node_modules/superagent": {
			"version": "9.0.2",
			"resolved": "https://registry.npmjs.org/superagent/-/superagent-9.0.2.tgz",
			"integrity": "sha512-xuW7dzkUpcJq7QnhOsnNUgtYp3xRwpt2F7abdRYIpCsAt0hhUqia0EdxyXZQQpNmGtsCzYHryaKSV3q3GJnq7w==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"component-emitter": "^1.3.0",
				"cookiejar": "^2.1.4",
				"debug": "^4.3.4",
				"fast-safe-stringify": "^2.1.1",
				"form-data": "^4.0.0",
				"formidable": "^3.5.1",
				"methods": "^1.1.2",
				"mime": "2.6.0",
				"qs": "^6.11.0"
			},
			"engines": {
				"node": ">=14.18.0"
			}
		},
		"node_modules/supertest": {
			"version": "7.1.0",
			"resolved": "https://registry.npmjs.org/supertest/-/supertest-7.1.0.tgz",
			"integrity": "sha512-5QeSO8hSrKghtcWEoPiO036fxH0Ii2wVQfFZSP0oqQhmjk8bOLhDFXr4JrvaFmPuEWUoq4znY3uSi8UzLKxGqw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"methods": "^1.1.2",
				"superagent": "^9.0.1"
			},
			"engines": {
				"node": ">=14.18.0"
			}
		},
		"node_modules/supports-color": {
			"version": "7.2.0",
			"resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz",
			"integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"has-flag": "^4.0.0"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/supports-preserve-symlinks-flag": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz",
			"integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">= 0.4"
			},
			"funding": {
				"url": "https://github.com/sponsors/ljharb"
			}
		},
		"node_modules/swr": {
			"version": "2.3.3",
			"resolved": "https://registry.npmjs.org/swr/-/swr-2.3.3.tgz",
			"integrity": "sha512-dshNvs3ExOqtZ6kJBaAsabhPdHyeY4P2cKwRCniDVifBMoG/SVI7tfLWqPXriVspf2Rg4tPzXJTnwaihIeFw2A==",
			"license": "MIT",
			"dependencies": {
				"dequal": "^2.0.3",
				"use-sync-external-store": "^1.4.0"
			},
			"peerDependencies": {
				"react": "^16.11.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
			}
		},
		"node_modules/term-size": {
			"version": "2.2.1",
			"resolved": "https://registry.npmjs.org/term-size/-/term-size-2.2.1.tgz",
			"integrity": "sha512-wK0Ri4fOGjv/XPy8SBHZChl8CM7uMc5VML7SqiQ0zG7+J5Vr+RMQDoHa2CNT6KHUnTGIXH34UDMkPzAUyapBZg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=8"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/test-exclude": {
			"version": "6.0.0",
			"resolved": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz",
			"integrity": "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"@istanbuljs/schema": "^0.1.2",
				"glob": "^7.1.4",
				"minimatch": "^3.0.4"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/throttleit": {
			"version": "2.1.0",
			"resolved": "https://registry.npmjs.org/throttleit/-/throttleit-2.1.0.tgz",
			"integrity": "sha512-nt6AMGKW1p/70DF/hGBdJB57B8Tspmbp5gfJ8ilhLnt7kkr2ye7hzD6NVG8GGErk2HWF34igrL2CXmNIkzKqKw==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/tinycolor2": {
			"version": "1.6.0",
			"resolved": "https://registry.npmjs.org/tinycolor2/-/tinycolor2-1.6.0.tgz",
			"integrity": "sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==",
			"license": "MIT"
		},
		"node_modules/tinygradient": {
			"version": "1.1.5",
			"resolved": "https://registry.npmjs.org/tinygradient/-/tinygradient-1.1.5.tgz",
			"integrity": "sha512-8nIfc2vgQ4TeLnk2lFj4tRLvvJwEfQuabdsmvDdQPT0xlk9TaNtpGd6nNRxXoK6vQhN6RSzj+Cnp5tTQmpxmbw==",
			"license": "MIT",
			"dependencies": {
				"@types/tinycolor2": "^1.4.0",
				"tinycolor2": "^1.0.0"
			}
		},
		"node_modules/tmp": {
			"version": "0.0.33",
			"resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz",
			"integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==",
			"license": "MIT",
			"dependencies": {
				"os-tmpdir": "~1.0.2"
			},
			"engines": {
				"node": ">=0.6.0"
			}
		},
		"node_modules/tmpl": {
			"version": "1.0.5",
			"resolved": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz",
			"integrity": "sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==",
			"dev": true,
			"license": "BSD-3-Clause"
		},
		"node_modules/to-regex-range": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz",
			"integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"is-number": "^7.0.0"
			},
			"engines": {
				"node": ">=8.0"
			}
		},
		"node_modules/toidentifier": {
			"version": "1.0.1",
			"resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz",
			"integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==",
			"license": "MIT",
			"engines": {
				"node": ">=0.6"
			}
		},
		"node_modules/token-types": {
			"version": "6.0.0",
			"resolved": "https://registry.npmjs.org/token-types/-/token-types-6.0.0.tgz",
			"integrity": "sha512-lbDrTLVsHhOMljPscd0yitpozq7Ga2M5Cvez5AjGg8GASBjtt6iERCAJ93yommPmz62fb45oFIXHEZ3u9bfJEA==",
			"license": "MIT",
			"dependencies": {
				"@tokenizer/token": "^0.3.0",
				"ieee754": "^1.2.1"
			},
			"engines": {
				"node": ">=14.16"
			},
			"funding": {
				"type": "github",
				"url": "https://github.com/sponsors/Borewit"
			}
		},
		"node_modules/tr46": {
			"version": "3.0.0",
			"resolved": "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz",
			"integrity": "sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==",
			"license": "MIT",
			"dependencies": {
				"punycode": "^2.1.1"
			},
			"engines": {
				"node": ">=12"
			}
		},
		"node_modules/tslib": {
			"version": "2.8.1",
			"resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz",
			"integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==",
			"license": "0BSD"
		},
		"node_modules/tsx": {
			"version": "4.19.3",
			"resolved": "https://registry.npmjs.org/tsx/-/tsx-4.19.3.tgz",
			"integrity": "sha512-4H8vUNGNjQ4V2EOoGw005+c+dGuPSnhpPBPHBtsZdGZBk/iJb4kguGlPWaZTZ3q5nMtFOEsY0nRDlh9PJyd6SQ==",
			"dev": true,
			"license": "MIT",
			"dependencies": {
				"esbuild": "~0.25.0",
				"get-tsconfig": "^4.7.5"
			},
			"bin": {
				"tsx": "dist/cli.mjs"
			},
			"engines": {
				"node": ">=18.0.0"
			},
			"optionalDependencies": {
				"fsevents": "~2.3.3"
			}
		},
		"node_modules/type-detect": {
			"version": "4.0.8",
			"resolved": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz",
			"integrity": "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=4"
			}
		},
		"node_modules/type-fest": {
			"version": "4.37.0",
			"resolved": "https://registry.npmjs.org/type-fest/-/type-fest-4.37.0.tgz",
			"integrity": "sha512-S/5/0kFftkq27FPNye0XM1e2NsnoD/3FS+pBmbjmmtLT6I+i344KoOf7pvXreaFsDamWeaJX55nczA1m5PsBDg==",
			"license": "(MIT OR CC0-1.0)",
			"engines": {
				"node": ">=16"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/type-is": {
			"version": "1.6.18",
			"resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz",
			"integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==",
			"license": "MIT",
			"dependencies": {
				"media-typer": "0.3.0",
				"mime-types": "~2.1.24"
			},
			"engines": {
				"node": ">= 0.6"
			}
		},
		"node_modules/uint8array-extras": {
			"version": "1.4.0",
			"resolved": "https://registry.npmjs.org/uint8array-extras/-/uint8array-extras-1.4.0.tgz",
			"integrity": "sha512-ZPtzy0hu4cZjv3z5NW9gfKnNLjoz4y6uv4HlelAjDK7sY/xOkKZv9xK/WQpcsBB3jEybChz9DPC2U/+cusjJVQ==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/undici": {
			"version": "7.10.0",
			"resolved": "https://registry.npmjs.org/undici/-/undici-7.10.0.tgz",
			"integrity": "sha512-u5otvFBOBZvmdjWLVW+5DAc9Nkq8f24g0O9oY7qw2JVIF1VocIFoyz9JFkuVOS2j41AufeO0xnlweJ2RLT8nGw==",
			"license": "MIT",
			"engines": {
				"node": ">=20.18.1"
			}
		},
		"node_modules/undici-types": {
			"version": "5.26.5",
			"resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz",
			"integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==",
			"license": "MIT"
		},
		"node_modules/unicorn-magic": {
			"version": "0.3.0",
			"resolved": "https://registry.npmjs.org/unicorn-magic/-/unicorn-magic-0.3.0.tgz",
			"integrity": "sha512-+QBBXBCvifc56fsbuxZQ6Sic3wqqc3WWaqxs58gvJrcOuN83HGTCwz3oS5phzU9LthRNE9VrJCFCLUgHeeFnfA==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/universalify": {
			"version": "0.1.2",
			"resolved": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz",
			"integrity": "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">= 4.0.0"
			}
		},
		"node_modules/unpipe": {
			"version": "1.0.0",
			"resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz",
			"integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/update-browserslist-db": {
			"version": "1.1.3",
			"resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz",
			"integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==",
			"dev": true,
			"funding": [
				{
					"type": "opencollective",
					"url": "https://opencollective.com/browserslist"
				},
				{
					"type": "tidelift",
					"url": "https://tidelift.com/funding/github/npm/browserslist"
				},
				{
					"type": "github",
					"url": "https://github.com/sponsors/ai"
				}
			],
			"license": "MIT",
			"dependencies": {
				"escalade": "^3.2.0",
				"picocolors": "^1.1.1"
			},
			"bin": {
				"update-browserslist-db": "cli.js"
			},
			"peerDependencies": {
				"browserslist": ">= 4.21.0"
			}
		},
		"node_modules/uri-js": {
			"version": "4.4.1",
			"resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz",
			"integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==",
			"license": "BSD-2-Clause",
			"dependencies": {
				"punycode": "^2.1.0"
			}
		},
		"node_modules/uri-templates": {
			"version": "0.2.0",
			"resolved": "https://registry.npmjs.org/uri-templates/-/uri-templates-0.2.0.tgz",
			"integrity": "sha512-EWkjYEN0L6KOfEoOH6Wj4ghQqU7eBZMJqRHQnxQAq+dSEzRPClkWjf8557HkWQXF6BrAUoLSAyy9i3RVTliaNg==",
			"license": "http://geraintluff.github.io/tv4/LICENSE.txt"
		},
		"node_modules/use-sync-external-store": {
			"version": "1.5.0",
			"resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz",
			"integrity": "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==",
			"license": "MIT",
			"peerDependencies": {
				"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
			}
		},
		"node_modules/utils-merge": {
			"version": "1.0.1",
			"resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz",
			"integrity": "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.4.0"
			}
		},
		"node_modules/uuid": {
			"version": "11.1.0",
			"resolved": "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz",
			"integrity": "sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==",
			"funding": [
				"https://github.com/sponsors/broofa",
				"https://github.com/sponsors/ctavan"
			],
			"license": "MIT",
			"bin": {
				"uuid": "dist/esm/bin/uuid"
			}
		},
		"node_modules/v8-to-istanbul": {
			"version": "9.3.0",
			"resolved": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz",
			"integrity": "sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"@jridgewell/trace-mapping": "^0.3.12",
				"@types/istanbul-lib-coverage": "^2.0.1",
				"convert-source-map": "^2.0.0"
			},
			"engines": {
				"node": ">=10.12.0"
			}
		},
		"node_modules/vary": {
			"version": "1.1.2",
			"resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz",
			"integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==",
			"license": "MIT",
			"engines": {
				"node": ">= 0.8"
			}
		},
		"node_modules/walker": {
			"version": "1.0.8",
			"resolved": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz",
			"integrity": "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==",
			"dev": true,
			"license": "Apache-2.0",
			"dependencies": {
				"makeerror": "1.0.12"
			}
		},
		"node_modules/web-streams-polyfill": {
			"version": "4.0.0-beta.3",
			"resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz",
			"integrity": "sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==",
			"license": "MIT",
			"engines": {
				"node": ">= 14"
			}
		},
		"node_modules/webidl-conversions": {
			"version": "7.0.0",
			"resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz",
			"integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==",
			"license": "BSD-2-Clause",
			"engines": {
				"node": ">=12"
			}
		},
		"node_modules/whatwg-url": {
			"version": "11.0.0",
			"resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz",
			"integrity": "sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==",
			"license": "MIT",
			"dependencies": {
				"tr46": "^3.0.0",
				"webidl-conversions": "^7.0.0"
			},
			"engines": {
				"node": ">=12"
			}
		},
		"node_modules/which": {
			"version": "2.0.2",
			"resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz",
			"integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==",
			"license": "ISC",
			"dependencies": {
				"isexe": "^2.0.0"
			},
			"bin": {
				"node-which": "bin/node-which"
			},
			"engines": {
				"node": ">= 8"
			}
		},
		"node_modules/widest-line": {
			"version": "5.0.0",
			"resolved": "https://registry.npmjs.org/widest-line/-/widest-line-5.0.0.tgz",
			"integrity": "sha512-c9bZp7b5YtRj2wOe6dlj32MK+Bx/M/d+9VB2SHM1OtsUHR0aV0tdP6DWh/iMt0kWi1t5g1Iudu6hQRNd1A4PVA==",
			"license": "MIT",
			"dependencies": {
				"string-width": "^7.0.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/wrap-ansi": {
			"version": "9.0.0",
			"resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-9.0.0.tgz",
			"integrity": "sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==",
			"license": "MIT",
			"dependencies": {
				"ansi-styles": "^6.2.1",
				"string-width": "^7.0.0",
				"strip-ansi": "^7.1.0"
			},
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/chalk/wrap-ansi?sponsor=1"
			}
		},
		"node_modules/wrap-ansi/node_modules/ansi-styles": {
			"version": "6.2.1",
			"resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz",
			"integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",
			"license": "MIT",
			"engines": {
				"node": ">=12"
			},
			"funding": {
				"url": "https://github.com/chalk/ansi-styles?sponsor=1"
			}
		},
		"node_modules/wrappy": {
			"version": "1.0.2",
			"resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz",
			"integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==",
			"license": "ISC"
		},
		"node_modules/write-file-atomic": {
			"version": "4.0.2",
			"resolved": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz",
			"integrity": "sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==",
			"dev": true,
			"license": "ISC",
			"dependencies": {
				"imurmurhash": "^0.1.4",
				"signal-exit": "^3.0.7"
			},
			"engines": {
				"node": "^12.13.0 || ^14.15.0 || >=16.0.0"
			}
		},
		"node_modules/write-file-atomic/node_modules/signal-exit": {
			"version": "3.0.7",
			"resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz",
			"integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==",
			"dev": true,
			"license": "ISC"
		},
		"node_modules/ws": {
			"version": "8.18.1",
			"resolved": "https://registry.npmjs.org/ws/-/ws-8.18.1.tgz",
			"integrity": "sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w==",
			"devOptional": true,
			"license": "MIT",
			"engines": {
				"node": ">=10.0.0"
			},
			"peerDependencies": {
				"bufferutil": "^4.0.1",
				"utf-8-validate": ">=5.0.2"
			},
			"peerDependenciesMeta": {
				"bufferutil": {
					"optional": true
				},
				"utf-8-validate": {
					"optional": true
				}
			}
		},
		"node_modules/xsschema": {
			"version": "0.3.0-beta.1",
			"resolved": "https://registry.npmjs.org/xsschema/-/xsschema-0.3.0-beta.1.tgz",
			"integrity": "sha512-Z7ZlPKLTc8iUKVfic0Lr66NB777wJqZl3JVLIy1vaNxx6NNTuylYm4wbK78Sgg7kHwaPRqFnuT4IliQM1sDxvg==",
			"license": "MIT",
			"peerDependencies": {
				"@valibot/to-json-schema": "^1.0.0",
				"arktype": "^2.1.16",
				"effect": "^3.14.5",
				"sury": "^10.0.0-rc",
				"zod": "^3.25.0",
				"zod-to-json-schema": "^3.24.5"
			},
			"peerDependenciesMeta": {
				"@valibot/to-json-schema": {
					"optional": true
				},
				"arktype": {
					"optional": true
				},
				"effect": {
					"optional": true
				},
				"sury": {
					"optional": true
				},
				"zod": {
					"optional": true
				},
				"zod-to-json-schema": {
					"optional": true
				}
			}
		},
		"node_modules/y18n": {
			"version": "5.0.8",
			"resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz",
			"integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==",
			"license": "ISC",
			"engines": {
				"node": ">=10"
			}
		},
		"node_modules/yallist": {
			"version": "3.1.1",
			"resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz",
			"integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==",
			"dev": true,
			"license": "ISC"
		},
		"node_modules/yargs": {
			"version": "17.7.2",
			"resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz",
			"integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==",
			"license": "MIT",
			"dependencies": {
				"cliui": "^8.0.1",
				"escalade": "^3.1.1",
				"get-caller-file": "^2.0.5",
				"require-directory": "^2.1.1",
				"string-width": "^4.2.3",
				"y18n": "^5.0.5",
				"yargs-parser": "^21.1.1"
			},
			"engines": {
				"node": ">=12"
			}
		},
		"node_modules/yargs-parser": {
			"version": "21.1.1",
			"resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz",
			"integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==",
			"license": "ISC",
			"engines": {
				"node": ">=12"
			}
		},
		"node_modules/yargs/node_modules/ansi-regex": {
			"version": "5.0.1",
			"resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
			"integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
			"license": "MIT",
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/yargs/node_modules/emoji-regex": {
			"version": "8.0.0",
			"resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
			"integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
			"license": "MIT"
		},
		"node_modules/yargs/node_modules/string-width": {
			"version": "4.2.3",
			"resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
			"integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
			"license": "MIT",
			"dependencies": {
				"emoji-regex": "^8.0.0",
				"is-fullwidth-code-point": "^3.0.0",
				"strip-ansi": "^6.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/yargs/node_modules/strip-ansi": {
			"version": "6.0.1",
			"resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
			"integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
			"license": "MIT",
			"dependencies": {
				"ansi-regex": "^5.0.1"
			},
			"engines": {
				"node": ">=8"
			}
		},
		"node_modules/yocto-queue": {
			"version": "0.1.0",
			"resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz",
			"integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==",
			"dev": true,
			"license": "MIT",
			"engines": {
				"node": ">=10"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/yoctocolors": {
			"version": "2.1.1",
			"resolved": "https://registry.npmjs.org/yoctocolors/-/yoctocolors-2.1.1.tgz",
			"integrity": "sha512-GQHQqAopRhwU8Kt1DDM8NjibDXHC8eoh1erhGAJPEyveY9qqVeXvVikNKrDz69sHowPMorbPUrH/mx8c50eiBQ==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/yoctocolors-cjs": {
			"version": "2.1.2",
			"resolved": "https://registry.npmjs.org/yoctocolors-cjs/-/yoctocolors-cjs-2.1.2.tgz",
			"integrity": "sha512-cYVsTjKl8b+FrnidjibDWskAv7UKOfcwaVZdp/it9n1s9fU3IkgDbhdIRKCW4JDsAlECJY0ytoVPT3sK6kideA==",
			"license": "MIT",
			"engines": {
				"node": ">=18"
			},
			"funding": {
				"url": "https://github.com/sponsors/sindresorhus"
			}
		},
		"node_modules/yoga-layout": {
			"version": "3.2.1",
			"resolved": "https://registry.npmjs.org/yoga-layout/-/yoga-layout-3.2.1.tgz",
			"integrity": "sha512-0LPOt3AxKqMdFBZA3HBAt/t/8vIKq7VaQYbuA8WxCgung+p9TVyKRYdpvCb80HcdTN2NkbIKbhNwKUfm3tQywQ==",
			"dev": true,
			"license": "MIT"
		},
		"node_modules/zod": {
			"version": "3.25.56",
			"resolved": "https://registry.npmjs.org/zod/-/zod-3.25.56.tgz",
			"integrity": "sha512-rd6eEF3BTNvQnR2e2wwolfTmUTnp70aUTqr0oaGbHifzC3BKJsoV+Gat8vxUMR1hwOKBs6El+qWehrHbCpW6SQ==",
			"license": "MIT",
			"funding": {
				"url": "https://github.com/sponsors/colinhacks"
			}
		},
		"node_modules/zod-to-json-schema": {
			"version": "3.24.5",
			"resolved": "https://registry.npmjs.org/zod-to-json-schema/-/zod-to-json-schema-3.24.5.tgz",
			"integrity": "sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==",
			"license": "ISC",
			"peerDependencies": {
				"zod": "^3.24.1"
			}
		}
	}
}
