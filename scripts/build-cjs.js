#!/usr/bin/env node

/**
 * CommonJS bundling script using esbuild
 * Fixes dynamic require issues by using CommonJS format
 */

import { build } from 'esbuild';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🔧 Building CommonJS bundles...\n');

// Build configurations
const builds = [
    {
        name: 'CLI Tool (CJS)',
        input: 'bin/task-master.js',
        output: 'dist/task-master-cjs.js'
    },
    {
        name: 'MCP Server (CJS)',
        input: 'mcp-server/server.js',
        output: 'dist/task-master-mcp-cjs.js'
    }
];

// CommonJS build options
const buildOptions = {
    bundle: true,
    platform: 'node',
    target: 'node18',
    format: 'cjs',  // Use CommonJS instead of ESM
    minify: false,
    sourcemap: false,
    external: [
        // Keep Node.js built-ins external - they work better with require()
        'fs', 'path', 'os', 'crypto', 'util', 'events', 'stream', 'url',
        'querystring', 'http', 'https', 'net', 'tls', 'zlib', 'child_process',
        'cluster', 'worker_threads', 'readline', 'assert', 'buffer', 'console',
        'constants', 'dgram', 'dns', 'domain', 'module', 'perf_hooks',
        'process', 'punycode', 'string_decoder', 'timers', 'tty', 'v8', 'vm',
        'fsevents', 'cpu-features'
    ],
    define: {
        'process.env.NODE_ENV': '"production"',
        'process.env.BUNDLED': '"true"'
    },
    // Handle ESM to CJS conversion
    banner: {
        js: `#!/usr/bin/env node
// CommonJS bundle - all dependencies included
const { createRequire } = require('module');
const __require = createRequire(import.meta ? import.meta.url : __filename);
`
    }
};

// Build function
async function buildFile(config) {
    console.log(`📦 Building ${config.name}...`);
    console.log(`   Input: ${config.input}`);
    console.log(`   Output: ${config.output}`);
    
    try {
        await build({
            ...buildOptions,
            entryPoints: [config.input],
            outfile: config.output
        });
        
        // Make executable
        fs.chmodSync(config.output, '755');
        
        // Get file size
        const stats = fs.statSync(config.output);
        const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        
        console.log(`   ✅ Built successfully (${fileSizeInMB} MB)`);
        return true;
        
    } catch (error) {
        console.error(`   ❌ Build failed: ${error.message}`);
        return false;
    }
}

// Create dist directory
const distDir = path.join(projectRoot, 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
}

// Build all files
async function buildAll() {
    let successCount = 0;
    
    for (const config of builds) {
        const success = await buildFile(config);
        if (success) successCount++;
        console.log('');
    }
    
    console.log('📊 CommonJS Build Summary:');
    console.log(`✅ Successful: ${successCount}/${builds.length}`);
    
    if (successCount === builds.length) {
        console.log('\n🎉 CommonJS builds completed successfully!');
        console.log('\n📋 Test the builds:');
        console.log('  ./dist/task-master-cjs.js --version');
        console.log('  ./dist/task-master-mcp-cjs.js --help');
        
    } else {
        console.log('\n⚠️  Some builds failed.');
        process.exit(1);
    }
}

buildAll().catch(console.error);
