name: Multi-Registry Release

on:
  workflow_dispatch:
    inputs:
      target_registries:
        description: 'Target registries (comma-separated)'
        required: true
        default: 'npm,private'
      version_bump:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - prerelease

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      registries: ${{ steps.parse.outputs.registries }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install Dependencies
        run: npm ci

      - name: Run Tests
        run: npm test

      - name: Parse Target Registries
        id: parse
        run: |
          REGISTRIES='${{ github.event.inputs.target_registries }}'
          echo "registries=$(echo $REGISTRIES | jq -R -s -c 'split(",")')" >> $GITHUB_OUTPUT

      - name: Bump Version
        id: version
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          NEW_VERSION=$(npm version ${{ github.event.inputs.version_bump }} --no-git-tag-version)
          echo "version=${NEW_VERSION#v}" >> $GITHUB_OUTPUT
          
          git add package.json
          git commit -m "chore: bump version to $NEW_VERSION"
          git push

  publish-npm:
    needs: prepare
    if: contains(github.event.inputs.target_registries, 'npm')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: main

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://registry.npmjs.org'

      - name: Install Dependencies
        run: npm ci

      - name: Publish to NPM
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  publish-private:
    needs: prepare
    if: contains(github.event.inputs.target_registries, 'private')
    runs-on: ubuntu-latest
    strategy:
      matrix:
        registry:
          - name: "Private NPM"
            url: "https://npm.your-company.com/"
            token_secret: "PRIVATE_NPM_TOKEN"
            scope: "your-company"
          - name: "Nexus"
            url: "https://nexus.your-company.com/repository/npm-private/"
            token_secret: "NEXUS_NPM_TOKEN"
            scope: "nexus"
    steps:
      - uses: actions/checkout@v4
        with:
          ref: main

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Dependencies
        run: npm ci

      - name: Configure Registry
        run: |
          echo "${{ matrix.registry.url }}:_authToken=${{ secrets[matrix.registry.token_secret] }}" >> ~/.npmrc
          echo "registry=${{ matrix.registry.url }}" >> ~/.npmrc

      - name: Update Package for Private Registry
        run: |
          node -e "
            const fs = require('fs');
            const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            
            // Add scope for private registry
            if ('${{ matrix.registry.scope }}') {
              pkg.name = '@${{ matrix.registry.scope }}/' + pkg.name.replace(/^@[^/]+\//, '');
            }
            
            // Add publishConfig
            pkg.publishConfig = {
              registry: '${{ matrix.registry.url }}',
              access: 'restricted'
            };
            
            fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
          "

      - name: Publish to ${{ matrix.registry.name }}
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets[matrix.registry.token_secret] }}

  create-release:
    needs: [prepare, publish-npm, publish-private]
    if: always() && needs.prepare.result == 'success'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ needs.prepare.outputs.version }}
          release_name: Release v${{ needs.prepare.outputs.version }}
          body: |
            ## Multi-Registry Release v${{ needs.prepare.outputs.version }}
            
            Published to the following registries:
            ${{ github.event.inputs.target_registries }}
            
            ### Installation Instructions
            
            #### From NPM (Public)
            ```bash
            npm install -g task-master-ai
            npx task-master-ai --help
            ```
            
            #### From Private Registry
            ```bash
            # Configure registry
            npm config set registry https://npm.your-company.com/
            
            # Install
            npm install -g @your-company/task-master-ai
            npx @your-company/task-master-ai --help
            ```
            
            ### MCP Configuration
            
            #### Public NPM
            ```json
            {
              "mcpServers": {
                "task-master": {
                  "command": "npx",
                  "args": ["-y", "--package=task-master-ai", "task-master-mcp"]
                }
              }
            }
            ```
            
            #### Private Registry
            ```json
            {
              "mcpServers": {
                "task-master": {
                  "command": "npx",
                  "args": ["-y", "--package=@your-company/task-master-ai", "task-master-mcp"]
                }
              }
            }
            ```
          draft: false
          prerelease: false
