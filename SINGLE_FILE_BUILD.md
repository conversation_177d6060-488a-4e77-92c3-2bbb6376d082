# 单文件编译指南

## 🎯 概述

本指南介绍如何将整个Node.js项目编译成包含所有依赖的单个可执行文件。

## 🚀 快速开始

### 方法1：esbuild (推荐)

```bash
# 安装依赖
npm install

# 构建单文件
npm run build:single

# 构建优化版本
npm run build:optimized

# 运行生成的文件
./dist/task-master-standalone.js --help
./dist/task-master-mcp-standalone.js
```

### 方法2：手动打包 (最简单)

```bash
# 手动打包 - 无需额外依赖
npm run build:manual

# 运行手动打包的文件
./dist/task-master-manual.js --help
./dist/task-master-mcp-manual.js
```

### 方法3：构建所有版本

```bash
# 构建所有版本
npm run build:all

# 查看生成的文件
ls -la dist/
```

## 📦 生成的文件

构建完成后，`dist/` 目录将包含：

```
dist/
├── task-master-standalone.js          # esbuild版本 (CLI工具)
├── task-master-mcp-standalone.js      # esbuild版本 (MCP服务器)
├── task-master-optimized.js           # 优化版本 (CLI工具)
├── task-master-mcp-optimized.js       # 优化版本 (MCP服务器)
├── task-master-manual.js              # 手动打包版本 (CLI工具)
├── task-master-mcp-manual.js          # 手动打包版本 (MCP服务器)
└── *.meta.json                        # 构建分析文件
```

## 🔧 使用方法

### CLI工具使用

```bash
# 直接运行
./dist/task-master-standalone.js --help

# 解析PRD
./dist/task-master-standalone.js parse-prd requirements.md

# 添加任务
./dist/task-master-standalone.js add-task "实现用户认证"

# 列出任务
./dist/task-master-standalone.js list-tasks
```

### MCP服务器使用

```bash
# 启动MCP服务器
./dist/task-master-mcp-standalone.js

# 在Claude Desktop配置中使用
{
  "mcpServers": {
    "task-master": {
      "command": "/path/to/dist/task-master-mcp-standalone.js"
    }
  }
}
```

## 📋 构建选项对比

| 方法 | 优点 | 缺点 | 文件大小 | 兼容性 |
|------|------|------|----------|--------|
| **esbuild** | 快速、现代、优化好 | 需要额外依赖 | 中等 | 高 |
| **手动打包** | 简单、无额外依赖 | 功能有限 | 小 | 中等 |
| **webpack** | 功能强大、插件丰富 | 配置复杂 | 大 | 高 |
| **rollup** | 树摇优化好 | 配置复杂 | 小 | 高 |

## 🎯 推荐使用场景

### esbuild (推荐)
- **适用于**：生产环境、需要优化的场景
- **优势**：构建速度快、输出优化、支持现代JS特性
- **命令**：`npm run build:single`

### 手动打包
- **适用于**：快速原型、简单部署、无网络环境
- **优势**：无额外依赖、构建简单、文件小
- **命令**：`npm run build:manual`

## 🚀 部署和分发

### 1. 本地部署

```bash
# 复制到目标系统
scp dist/task-master-standalone.js user@server:/usr/local/bin/task-master
ssh user@server chmod +x /usr/local/bin/task-master

# 使用
ssh user@server task-master --help
```

### 2. Docker部署

```dockerfile
FROM node:18-alpine
COPY dist/task-master-standalone.js /usr/local/bin/task-master
RUN chmod +x /usr/local/bin/task-master
ENTRYPOINT ["task-master"]
```

### 3. 系统服务

```bash
# 创建systemd服务
sudo tee /etc/systemd/system/task-master-mcp.service << EOF
[Unit]
Description=Task Master MCP Server
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/task-master-mcp-standalone.js
Restart=always
User=taskmaster

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable task-master-mcp
sudo systemctl start task-master-mcp
```

## 🔍 故障排除

### 常见问题

#### 1. 权限错误

```bash
# 确保文件可执行
chmod +x dist/task-master-standalone.js

# 检查shebang
head -1 dist/task-master-standalone.js
# 应该显示: #!/usr/bin/env node
```

#### 2. 模块找不到

```bash
# 检查Node.js版本
node --version  # 需要 >= 18.0.0

# 检查文件完整性
ls -la dist/task-master-standalone.js
```

#### 3. 运行时错误

```bash
# 启用调试模式
DEBUG=* ./dist/task-master-standalone.js --help

# 检查构建日志
npm run build:single 2>&1 | tee build.log
```

### 调试技巧

```bash
# 分析包大小
npm run build:analyze

# 查看包含的模块
cat dist/task-master-standalone.meta.json | jq '.inputs'

# 测试基本功能
./dist/task-master-standalone.js --version
```

## ⚡ 性能优化

### 减小文件大小

```bash
# 使用优化构建
npm run build:optimized

# 排除不必要的依赖
# 编辑 scripts/build-single-file.js 中的 external 数组
```

### 提高启动速度

```bash
# 预编译配置
export TASKMASTER_CONFIG_CACHE=true

# 使用生产模式
export NODE_ENV=production
```

## 📊 构建分析

### 查看构建统计

```bash
# 构建并分析
npm run build:single
cat dist/task-master-standalone.meta.json | jq '.outputs'

# 查看依赖关系
npm ls --depth=0
```

### 优化建议

1. **移除未使用的依赖**
   ```bash
   npm audit
   npm prune
   ```

2. **使用更小的替代包**
   - 用 `node-fetch` 替代 `axios`
   - 用 `chalk` 替代 `colors`

3. **启用树摇优化**
   ```bash
   npm run build:optimized
   ```

## 🎉 成功验证

构建成功后，验证以下功能：

```bash
# 1. 基本功能
./dist/task-master-standalone.js --version
./dist/task-master-standalone.js --help

# 2. 核心功能
./dist/task-master-standalone.js models --setup
./dist/task-master-standalone.js add-task "测试任务"
./dist/task-master-standalone.js list-tasks

# 3. MCP服务器
./dist/task-master-mcp-standalone.js &
# 检查进程是否正常运行
ps aux | grep task-master-mcp
```

## 📞 支持

- **构建问题**：检查 `build.log` 文件
- **运行问题**：启用 `DEBUG=*` 模式
- **性能问题**：使用 `npm run build:optimized`

---

通过以上方法，您可以将整个Node.js项目打包成单个可执行文件，方便部署和分发！🚀
