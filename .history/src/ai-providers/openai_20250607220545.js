/**
 * openai.js
 * AI provider implementation for OpenAI models using Vercel AI SDK.
 */

import { createOpenAI } from '@ai-sdk/openai';
import { BaseAIProvider } from './base-provider.js';
import { log } from '../../scripts/modules/index.js';

export class OpenAIProvider extends BaseAIProvider {
	constructor() {
		super();
		this.name = 'OpenAI';
	}

	/**
	 * Checks if the model ID indicates a Claude model being proxied through OpenAI gateway
	 * @param {string} modelId - The model identifier
	 * @returns {boolean} True if this is a Claude model
	 */
	isClaudeModel(modelId) {
		if (!modelId) return false;
		const lowerModelId = modelId.toLowerCase();
		return lowerModelId.includes('claude') ||
		       lowerModelId.includes('anthropic') ||
		       lowerModelId.startsWith('claude-');
	}

	/**
	 * Converts Anthropic API response format to OpenAI format
	 * @param {Object} anthropicResponse - Raw response from Anthropic API
	 * @returns {Object} OpenAI-compatible response format
	 */
	convertAnthropicToOpenAIFormat(anthropicResponse) {
		try {
			// Handle Anthropic response format: { content: [{ text: "..." }], usage: {...} }
			if (anthropicResponse.content && Array.isArray(anthropicResponse.content)) {
				const textContent = anthropicResponse.content
					.filter(item => item.type === 'text')
					.map(item => item.text)
					.join('');

				return {
					choices: [{
						message: {
							content: textContent,
							role: 'assistant'
						},
						finish_reason: 'stop',
						index: 0
					}],
					usage: {
						prompt_tokens: anthropicResponse.usage?.input_tokens || 0,
						completion_tokens: anthropicResponse.usage?.output_tokens || 0,
						total_tokens: (anthropicResponse.usage?.input_tokens || 0) + (anthropicResponse.usage?.output_tokens || 0)
					}
				};
			}

			// Handle tool use responses for generateObject
			if (anthropicResponse.content && anthropicResponse.content.some(item => item.type === 'tool_use')) {
				const toolUse = anthropicResponse.content.find(item => item.type === 'tool_use');

				return {
					choices: [{
						message: {
							tool_calls: [{
								id: toolUse.id,
								type: 'function',
								function: {
									name: toolUse.name,
									arguments: JSON.stringify(toolUse.input)
								}
							}],
							role: 'assistant'
						},
						finish_reason: 'tool_calls',
						index: 0
					}],
					usage: {
						prompt_tokens: anthropicResponse.usage?.input_tokens || 0,
						completion_tokens: anthropicResponse.usage?.output_tokens || 0,
						total_tokens: (anthropicResponse.usage?.input_tokens || 0) + (anthropicResponse.usage?.output_tokens || 0)
					}
				};
			}

			// If it's already in OpenAI format, return as-is
			if (anthropicResponse.choices) {
				return anthropicResponse;
			}

			// Fallback: wrap unknown format
			log('warn', 'Unknown Anthropic response format, attempting basic conversion');
			return {
				choices: [{
					message: {
						content: JSON.stringify(anthropicResponse),
						role: 'assistant'
					},
					finish_reason: 'stop',
					index: 0
				}],
				usage: {
					prompt_tokens: 0,
					completion_tokens: 0,
					total_tokens: 0
				}
			};

		} catch (error) {
			log('error', `Error converting Anthropic response to OpenAI format: ${error.message}`);
			throw new Error(`Response format conversion failed: ${error.message}`);
		}
	}

	/**
	 * Creates and returns an OpenAI client instance.
	 * @param {object} params - Parameters for client initialization
	 * @param {string} params.apiKey - OpenAI API key
	 * @param {string} [params.baseURL] - Optional custom API endpoint
	 * @returns {Function} OpenAI client function
	 * @throws {Error} If API key is missing or initialization fails
	 */
	getClient(params) {
		try {
			const { apiKey, baseURL } = params;

			if (!apiKey) {
				throw new Error('OpenAI API key is required.');
			}

			return createOpenAI({
				apiKey,
				...(baseURL && { baseURL })
			});
		} catch (error) {
			this.handleError('client initialization', error);
		}
	}
}
