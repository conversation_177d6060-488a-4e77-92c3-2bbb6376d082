#!/usr/bin/env node

/**
 * Native Node.js bundling script
 * Creates self-contained scripts without external bundlers
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🏗️  Native Node.js bundling...\n');

// Read package.json
const packageJson = JSON.parse(fs.readFileSync(path.join(projectRoot, 'package.json'), 'utf8'));

// Create a self-contained script template
function createSelfContainedScript(entryFile, outputFile, scriptName) {
    console.log(`📦 Creating ${scriptName}...`);
    
    // Read the entry file
    const entryContent = fs.readFileSync(path.join(projectRoot, entryFile), 'utf8');
    
    // Create the bundle template
    const bundleTemplate = `#!/usr/bin/env node

/**
 * ${scriptName} - Self-contained bundle
 * Generated from: ${entryFile}
 * Build time: ${new Date().toISOString()}
 * 
 * This file contains the application with minimal dependencies.
 * Node.js built-in modules are used directly.
 */

// Set up environment
process.env.NODE_ENV = 'production';
process.env.BUNDLED = 'true';

// Import Node.js built-in modules
import { fileURLToPath } from 'url';
import { dirname, join, resolve } from 'path';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { homedir } from 'os';

// Set up __dirname and __filename for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Embedded package.json
const PACKAGE_JSON = ${JSON.stringify(packageJson, null, 2)};

// Mock some commonly used modules with simplified implementations
const mockModules = {
    chalk: {
        red: (text) => \`\\x1b[31m\${text}\\x1b[0m\`,
        green: (text) => \`\\x1b[32m\${text}\\x1b[0m\`,
        blue: (text) => \`\\x1b[34m\${text}\\x1b[0m\`,
        yellow: (text) => \`\\x1b[33m\${text}\\x1b[0m\`,
        gray: (text) => \`\\x1b[90m\${text}\\x1b[0m\`,
        cyan: (text) => \`\\x1b[36m\${text}\\x1b[0m\`,
        bold: {
            red: (text) => \`\\x1b[1m\\x1b[31m\${text}\\x1b[0m\`,
            green: (text) => \`\\x1b[1m\\x1b[32m\${text}\\x1b[0m\`,
            blue: (text) => \`\\x1b[1m\\x1b[34m\${text}\\x1b[0m\`,
            yellow: (text) => \`\\x1b[1m\\x1b[33m\${text}\\x1b[0m\`
        }
    }
};

// Simple module resolver
function requireMock(moduleName) {
    if (mockModules[moduleName]) {
        return mockModules[moduleName];
    }
    
    // For Node.js built-ins, use dynamic import
    try {
        return require(moduleName);
    } catch (error) {
        console.warn(\`Warning: Could not load module \${moduleName}\`);
        return {};
    }
}

// Override global require for mocked modules
global.requireMock = requireMock;

// === APPLICATION CODE STARTS HERE ===

`;

    // Process the entry content
    let processedContent = entryContent;
    
    // Remove the original shebang
    processedContent = processedContent.replace(/^#!/, '// #!/');
    
    // Simple import replacements for common modules
    const importReplacements = {
        'chalk': 'requireMock("chalk")',
        'commander': 'await import("commander")',
        'inquirer': 'await import("inquirer")',
        'fs': '{ readFileSync, writeFileSync, existsSync, mkdirSync }',
        'path': '{ dirname, join, resolve }',
        'os': '{ homedir }'
    };
    
    // Replace imports with our mocked versions or built-ins
    Object.entries(importReplacements).forEach(([module, replacement]) => {
        const importRegex = new RegExp(`import\\s+.*?\\s+from\\s+['"]${module}['"]`, 'g');
        processedContent = processedContent.replace(importRegex, `const ${module.replace(/[^a-zA-Z0-9]/g, '_')} = ${replacement}`);
    });
    
    // Combine template and processed content
    const finalBundle = bundleTemplate + processedContent;
    
    // Write the bundle
    fs.writeFileSync(outputFile, finalBundle);
    fs.chmodSync(outputFile, '755');
    
    const stats = fs.statSync(outputFile);
    const fileSizeInKB = (stats.size / 1024).toFixed(2);
    
    console.log(`   ✅ Created ${scriptName} (${fileSizeInKB} KB)`);
    return true;
}

// Create a more sophisticated bundle using only Node.js built-ins
function createMinimalBundle(entryFile, outputFile, scriptName) {
    console.log(`⚡ Creating minimal ${scriptName}...`);
    
    const entryContent = fs.readFileSync(path.join(projectRoot, entryFile), 'utf8');
    
    const minimalTemplate = `#!/usr/bin/env node

/**
 * ${scriptName} - Minimal Self-contained Bundle
 * Generated from: ${entryFile}
 * 
 * This version uses only Node.js built-in modules for maximum compatibility.
 */

import { fileURLToPath } from 'url';
import { dirname, join, resolve } from 'path';
import { readFileSync, writeFileSync, existsSync, mkdirSync, readdirSync, statSync } from 'fs';
import { homedir, platform } from 'os';
import { createRequire } from 'module';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const require = createRequire(import.meta.url);

// Environment setup
process.env.NODE_ENV = 'production';
process.env.BUNDLED = 'true';

// Simple console colors without external dependencies
const colors = {
    reset: '\\x1b[0m',
    red: '\\x1b[31m',
    green: '\\x1b[32m',
    yellow: '\\x1b[33m',
    blue: '\\x1b[34m',
    cyan: '\\x1b[36m',
    gray: '\\x1b[90m'
};

const log = {
    info: (msg) => console.log(\`\${colors.blue}[INFO]\${colors.reset} \${msg}\`),
    success: (msg) => console.log(\`\${colors.green}[SUCCESS]\${colors.reset} \${msg}\`),
    warn: (msg) => console.log(\`\${colors.yellow}[WARN]\${colors.reset} \${msg}\`),
    error: (msg) => console.error(\`\${colors.red}[ERROR]\${colors.reset} \${msg}\`),
    debug: (msg) => process.env.DEBUG && console.log(\`\${colors.gray}[DEBUG]\${colors.reset} \${msg}\`)
};

// Embedded configuration
const CONFIG = {
    version: "${packageJson.version}",
    name: "${packageJson.name}",
    description: "${packageJson.description}"
};

// === SIMPLIFIED APPLICATION LOGIC ===

`;

    // Create a very simplified version of the application
    const simplifiedApp = `
// Simplified application entry point
function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    switch (command) {
        case '--version':
        case '-v':
            console.log(CONFIG.version);
            break;
            
        case '--help':
        case '-h':
            console.log(\`
\${CONFIG.name} v\${CONFIG.version}
\${CONFIG.description}

Usage: \${process.argv[1]} [command] [options]

Commands:
  --version, -v     Show version
  --help, -h        Show this help
  models            Configure AI models
  parse-prd         Parse PRD document
  add-task          Add a new task
  list-tasks        List all tasks

This is a bundled version with limited functionality.
For full features, use the npm package: npm install -g ${packageJson.name}
\`);
            break;
            
        case 'models':
            log.info('Model configuration in bundled version is limited.');
            log.info('Please set environment variables:');
            log.info('  OPENAI_API_KEY=your-key');
            log.info('  ANTHROPIC_API_KEY=your-key');
            break;
            
        default:
            log.warn(\`Unknown command: \${command}\`);
            log.info('Use --help for available commands');
            process.exit(1);
    }
}

// Run the application
if (import.meta.url === \`file://\${process.argv[1]}\`) {
    main();
}
`;

    const finalBundle = minimalTemplate + simplifiedApp;
    
    fs.writeFileSync(outputFile, finalBundle);
    fs.chmodSync(outputFile, '755');
    
    const stats = fs.statSync(outputFile);
    const fileSizeInKB = (stats.size / 1024).toFixed(2);
    
    console.log(`   ✅ Created minimal ${scriptName} (${fileSizeInKB} KB)`);
    return true;
}

// Main build process
async function buildNativeBundles() {
    // Ensure dist directory exists
    const distDir = path.join(projectRoot, 'dist');
    if (!fs.existsSync(distDir)) {
        fs.mkdirSync(distDir, { recursive: true });
    }

    const builds = [
        {
            entry: 'bin/task-master.js',
            output: path.join(distDir, 'task-master-native.js'),
            name: 'CLI Tool (Native)'
        },
        {
            entry: 'mcp-server/server.js',
            output: path.join(distDir, 'task-master-mcp-native.js'),
            name: 'MCP Server (Native)'
        }
    ];

    let successCount = 0;

    for (const build of builds) {
        // Create minimal version (most compatible)
        if (createMinimalBundle(build.entry, build.output, build.name)) {
            successCount++;
        }
    }

    console.log(`\n📊 Native bundling completed: ${successCount}/${builds.length} successful`);

    if (successCount > 0) {
        console.log('\n🎉 Native bundles created successfully!');
        console.log('\n📋 Test the bundles:');
        console.log('  ./dist/task-master-native.js --version');
        console.log('  ./dist/task-master-mcp-native.js --help');
        console.log('\n📝 Note: These are minimal versions with basic functionality.');
        console.log('   For full features, use the npm package or esbuild version.');
    }
}

// Run the build
buildNativeBundles().catch(console.error);
