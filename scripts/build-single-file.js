#!/usr/bin/env node

/**
 * Single File Build Script
 * Bundles the entire Node.js application into a single executable file
 */

import { build } from 'esbuild';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🚀 Building single-file executable...\n');

// Build configurations
const buildConfigs = [
    {
        name: 'CLI Tool',
        entryPoint: 'bin/task-master.js',
        outfile: 'dist/task-master-standalone.js',
        description: 'Main CLI application'
    },
    {
        name: 'MCP Server',
        entryPoint: 'mcp-server/server.js',
        outfile: 'dist/task-master-mcp-standalone.js',
        description: 'MCP server for Claude Desktop'
    }
];

// Common esbuild options
const commonOptions = {
    bundle: true,
    platform: 'node',
    target: 'node18',
    format: 'esm',
    minify: true,
    sourcemap: false,
    external: [
        // Keep these as external if they cause issues
        'fsevents',
        'cpu-features'
    ],
    banner: {
        js: '#!/usr/bin/env node'
    },
    define: {
        'process.env.NODE_ENV': '"production"'
    },
    loader: {
        '.node': 'file',
        '.json': 'json'
    }
};

// Plugin to handle dynamic imports and require calls
const dynamicImportPlugin = {
    name: 'dynamic-import',
    setup(build) {
        // Handle dynamic imports
        build.onResolve({ filter: /.*/ }, (args) => {
            // Skip external modules
            if (commonOptions.external.includes(args.path)) {
                return { path: args.path, external: true };
            }
            return null;
        });
    }
};

// Plugin to inline assets
const inlineAssetsPlugin = {
    name: 'inline-assets',
    setup(build) {
        build.onLoad({ filter: /\.(txt|md|json)$/ }, async (args) => {
            const contents = await fs.promises.readFile(args.path, 'utf8');
            return {
                contents: `export default ${JSON.stringify(contents)}`,
                loader: 'js'
            };
        });
    }
};

// Build function
async function buildSingleFile(config) {
    console.log(`📦 Building ${config.name}...`);
    console.log(`   Entry: ${config.entryPoint}`);
    console.log(`   Output: ${config.outfile}`);
    
    try {
        const result = await build({
            ...commonOptions,
            entryPoints: [config.entryPoint],
            outfile: config.outfile,
            plugins: [dynamicImportPlugin, inlineAssetsPlugin],
            metafile: true
        });
        
        // Make the output file executable
        fs.chmodSync(config.outfile, '755');
        
        // Get file size
        const stats = fs.statSync(config.outfile);
        const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        
        console.log(`   ✅ Built successfully (${fileSizeInMB} MB)`);
        
        // Write metafile for analysis
        const metafilePath = config.outfile.replace('.js', '.meta.json');
        fs.writeFileSync(metafilePath, JSON.stringify(result.metafile, null, 2));
        
        return true;
        
    } catch (error) {
        console.error(`   ❌ Build failed: ${error.message}`);
        return false;
    }
}

// Create dist directory
const distDir = path.join(projectRoot, 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
}

// Build all configurations
async function buildAll() {
    let successCount = 0;
    
    for (const config of buildConfigs) {
        const success = await buildSingleFile(config);
        if (success) successCount++;
        console.log('');
    }
    
    // Summary
    console.log('📊 Build Summary:');
    console.log(`✅ Successful: ${successCount}/${buildConfigs.length}`);
    console.log(`📁 Output directory: ${distDir}`);
    
    if (successCount === buildConfigs.length) {
        console.log('\n🎉 All builds completed successfully!');
        console.log('\n📋 Usage:');
        console.log('  # CLI Tool');
        console.log('  ./dist/task-master-standalone.js --help');
        console.log('  ');
        console.log('  # MCP Server');
        console.log('  ./dist/task-master-mcp-standalone.js');
        console.log('');
        console.log('📦 Distribution:');
        console.log('  - Copy the standalone files to any system with Node.js');
        console.log('  - No npm install required');
        console.log('  - All dependencies are bundled');
        
    } else {
        console.log('\n⚠️  Some builds failed. Check the output above.');
        process.exit(1);
    }
}

// Advanced build with optimization
async function buildOptimized() {
    console.log('🔧 Building optimized single files...\n');
    
    const optimizedConfigs = buildConfigs.map(config => ({
        ...config,
        outfile: config.outfile.replace('.js', '-optimized.js')
    }));
    
    for (const config of optimizedConfigs) {
        console.log(`⚡ Building optimized ${config.name}...`);
        
        try {
            await build({
                ...commonOptions,
                entryPoints: [config.entryPoint],
                outfile: config.outfile,
                minify: true,
                treeShaking: true,
                plugins: [dynamicImportPlugin, inlineAssetsPlugin],
                define: {
                    ...commonOptions.define,
                    'process.env.DEBUG': 'false',
                    'process.env.NODE_ENV': '"production"'
                },
                drop: ['console', 'debugger'],
                legalComments: 'none'
            });
            
            fs.chmodSync(config.outfile, '755');
            
            const stats = fs.statSync(config.outfile);
            const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            
            console.log(`   ✅ Optimized build complete (${fileSizeInMB} MB)`);
            
        } catch (error) {
            console.error(`   ❌ Optimized build failed: ${error.message}`);
        }
        
        console.log('');
    }
}

// CLI interface
const command = process.argv[2];

switch (command) {
    case 'optimized':
        await buildOptimized();
        break;
    case 'analyze':
        console.log('📊 Analyzing bundle...');
        // You can add bundle analysis here
        break;
    default:
        await buildAll();
        break;
}

console.log('🏁 Build process completed!');
