name: Release to Private Registry

on:
  push:
    branches:
      - main
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      registry:
        description: 'Target registry'
        required: true
        default: 'private'
        type: choice
        options:
          - private
          - nexus
          - artifactory
          - gitlab
      version_type:
        description: 'Version type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - prerelease

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release-private:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            */*/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install Dependencies
        run: npm ci
        timeout-minutes: 2

      - name: Run Tests
        run: npm test
        env:
          NODE_ENV: test

      - name: Configure Private Registry
        run: |
          # Configure npm registry based on input or default
          case "${{ github.event.inputs.registry || 'private' }}" in
            "private")
              echo "//npm.your-company.com/:_authToken=${{ secrets.PRIVATE_NPM_TOKEN }}" >> ~/.npmrc
              echo "registry=https://npm.your-company.com/" >> ~/.npmrc
              echo "REGISTRY_URL=https://npm.your-company.com/" >> $GITHUB_ENV
              ;;
            "nexus")
              echo "//nexus.your-company.com/repository/npm-private/:_authToken=${{ secrets.NEXUS_NPM_TOKEN }}" >> ~/.npmrc
              echo "registry=https://nexus.your-company.com/repository/npm-private/" >> ~/.npmrc
              echo "REGISTRY_URL=https://nexus.your-company.com/repository/npm-private/" >> $GITHUB_ENV
              ;;
            "artifactory")
              echo "//your-company.jfrog.io/artifactory/api/npm/npm-local/:_authToken=${{ secrets.ARTIFACTORY_TOKEN }}" >> ~/.npmrc
              echo "registry=https://your-company.jfrog.io/artifactory/api/npm/npm-local/" >> ~/.npmrc
              echo "REGISTRY_URL=https://your-company.jfrog.io/artifactory/api/npm/npm-local/" >> $GITHUB_ENV
              ;;
            "gitlab")
              echo "//gitlab.your-company.com/api/v4/projects/${{ secrets.GITLAB_PROJECT_ID }}/packages/npm/:_authToken=${{ secrets.GITLAB_TOKEN }}" >> ~/.npmrc
              echo "registry=https://gitlab.your-company.com/api/v4/projects/${{ secrets.GITLAB_PROJECT_ID }}/packages/npm/" >> ~/.npmrc
              echo "REGISTRY_URL=https://gitlab.your-company.com/api/v4/projects/${{ secrets.GITLAB_PROJECT_ID }}/packages/npm/" >> $GITHUB_ENV
              ;;
          esac

      - name: Update Package for Private Registry
        run: |
          # Update package.json for private registry
          node -e "
            const fs = require('fs');
            const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            
            // Add publishConfig for private registry
            pkg.publishConfig = {
              registry: process.env.REGISTRY_URL,
              access: 'restricted'
            };
            
            // Update package name for private scope (optional)
            if (process.env.PRIVATE_SCOPE) {
              pkg.name = \`@\${process.env.PRIVATE_SCOPE}/\${pkg.name}\`;
            }
            
            fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
          "
        env:
          PRIVATE_SCOPE: ${{ secrets.PRIVATE_SCOPE }}

      - name: Version Package
        run: |
          # Configure git for version commits
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          # Version based on input or auto-detect
          if [ "${{ github.event.inputs.version_type }}" != "" ]; then
            npm version ${{ github.event.inputs.version_type }} --no-git-tag-version
          else
            # Use changesets for automatic versioning
            npx changeset version
          fi

      - name: Build Package
        run: |
          # Run any build steps
          npm run prepare-release || true
          
          # Test package contents
          npm pack --dry-run

      - name: Publish to Private Registry
        run: |
          echo "Publishing to: $REGISTRY_URL"
          npm publish --registry=$REGISTRY_URL
        env:
          NODE_AUTH_TOKEN: ${{ secrets.PRIVATE_NPM_TOKEN }}

      - name: Create Git Tag
        if: success()
        run: |
          VERSION=$(node -p "require('./package.json').version")
          git tag "v$VERSION"
          git push origin "v$VERSION"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create GitHub Release
        if: success()
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ env.VERSION }}
          release_name: Release v${{ env.VERSION }} (Private)
          body: |
            Released to private registry: ${{ env.REGISTRY_URL }}
            
            ## Installation
            ```bash
            # Configure your npm to use private registry
            npm config set registry ${{ env.REGISTRY_URL }}
            
            # Install the package
            npm install -g task-master-ai
            
            # Or use with npx
            npx task-master-ai --help
            ```
          draft: false
          prerelease: false

      - name: Notify Teams
        if: success()
        run: |
          # Send notification to Teams/Slack/etc
          echo "Package published successfully to private registry"
          # Add your notification logic here
