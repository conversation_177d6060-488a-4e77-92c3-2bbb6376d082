# Task ID: 6
# Title: Add YAML Configuration Management
# Status: pending
# Dependencies: 5
# Priority: medium
# Description: Enable flexible configuration via YAML files, with command-line overrides and parameter validation.
# Details:
- Use PyYAML to parse config files
- Define schema for all configurable parameters (API key, output format, image quality, etc.)
- Allow CLI arguments to override config values
- Implement validation and default value handling
- Document config options in README

# Test Strategy:
- Test with valid and invalid YAML files
- Check CLI override precedence
- Validate parameter validation and defaults
