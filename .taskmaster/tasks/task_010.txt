# Task ID: 10
# Title: Write Unit and Integration Tests
# Status: pending
# Dependencies: 9
# Priority: high
# Description: Develop comprehensive tests for all modules, including edge cases and integration flows.
# Details:
- Use pytest or unittest for test suite
- Cover PDF conversion, OCR, Markdown formatting, CLI, config, and batch processing
- Include tests for error handling and logging
- Set up CI workflow if possible

# Test Strategy:
- Run all tests and ensure high coverage
- Test with diverse PDF samples and config scenarios
- Validate integration tests for end-to-end workflow
