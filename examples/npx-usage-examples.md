# NPX Usage Examples for task-master-ai

## 🚀 Quick Start Examples

### 1. First Time Setup

```bash
# Run without installation - NPX will download and run
npx task-master-ai models --setup

# Follow the prompts to configure your AI models
```

### 2. Parse a PRD Document

```bash
# Parse a requirements document
npx task-master-ai parse-prd ./docs/requirements.md

# Parse with specific number of tasks
npx task-master-ai parse-prd ./docs/requirements.md --num-tasks 15

# Parse and skip task generation
npx task-master-ai parse-prd ./docs/requirements.md --skip-generate
```

### 3. Task Management

```bash
# Add a new task
npx task-master-ai add-task "Implement user authentication system"

# Add task with priority and details
npx task-master-ai add-task "Setup CI/CD pipeline" --priority high --details "Configure GitHub Actions for automated testing and deployment"

# List all tasks
npx task-master-ai list-tasks

# List tasks with specific status
npx task-master-ai list-tasks --status pending

# Update a task
npx task-master-ai update-task 1 --status "in-progress"

# Delete a task
npx task-master-ai delete-task 5
```

### 4. Export and Import

```bash
# Export tasks to JSON
npx task-master-ai export --format json --output ./tasks.json

# Export to CSV
npx task-master-ai export --format csv --output ./tasks.csv

# Export to Markdown
npx task-master-ai export --format markdown --output ./TASKS.md
```

## 🔧 Configuration Examples

### Environment Variables

```bash
# Set API keys
export OPENAI_API_KEY="sk-your-openai-key"
export ANTHROPIC_API_KEY="sk-ant-your-anthropic-key"

# Enable debug mode
export TASKMASTER_DEBUG=true

# Run with environment variables
npx task-master-ai parse-prd requirements.md
```

### Project-Specific Configuration

```bash
# Create project config directory
mkdir -p .taskmaster

# Run setup for this project
npx task-master-ai models --setup

# The config will be saved in .taskmaster/config.json
```

## 📁 Project Integration Examples

### 1. Package.json Scripts

Add to your project's `package.json`:

```json
{
  "scripts": {
    "tasks:setup": "npx task-master-ai models --setup",
    "tasks:parse": "npx task-master-ai parse-prd docs/requirements.md",
    "tasks:list": "npx task-master-ai list-tasks",
    "tasks:export": "npx task-master-ai export --format json --output tasks.json"
  }
}
```

Then use:

```bash
npm run tasks:setup
npm run tasks:parse
npm run tasks:list
```

### 2. Makefile Integration

```makefile
.PHONY: setup-tasks parse-prd list-tasks

setup-tasks:
	npx task-master-ai models --setup

parse-prd:
	npx task-master-ai parse-prd docs/requirements.md --num-tasks 10

list-tasks:
	npx task-master-ai list-tasks --format table

export-tasks:
	npx task-master-ai export --format json --output dist/tasks.json
```

### 3. GitHub Actions Workflow

```yaml
name: Process PRD
on:
  push:
    paths: ['docs/requirements.md']

jobs:
  process-prd:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Process PRD with Task Master
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          npx task-master-ai parse-prd docs/requirements.md
          
      - name: Commit generated tasks
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .taskmaster/
          git commit -m "Auto-generated tasks from PRD" || exit 0
          git push
```

## 🎯 Advanced Usage Examples

### 1. Batch Processing

```bash
# Process multiple PRD files
for file in docs/prd/*.md; do
  echo "Processing $file..."
  npx task-master-ai parse-prd "$file" --num-tasks 5
done

# Export all tasks after processing
npx task-master-ai export --format json --output all-tasks.json
```

### 2. Custom Configuration

```bash
# Use custom config file
npx task-master-ai --config ./custom-config.json parse-prd requirements.md

# Override specific settings
npx task-master-ai parse-prd requirements.md \
  --model gpt-4 \
  --max-tokens 2048 \
  --temperature 0.3
```

### 3. Integration with Other Tools

```bash
# Pipe output to other tools
npx task-master-ai list-tasks --format json | jq '.tasks[] | select(.priority == "high")'

# Use with grep for filtering
npx task-master-ai list-tasks --format text | grep "authentication"

# Generate tasks and immediately export
npx task-master-ai parse-prd requirements.md && \
npx task-master-ai export --format markdown --output TASKS.md
```

## 🐳 Docker Integration

```bash
# Run in Docker container
docker run --rm -v $(pwd):/workspace -w /workspace node:18 \
  npx task-master-ai parse-prd requirements.md

# With environment variables
docker run --rm \
  -e OPENAI_API_KEY="$OPENAI_API_KEY" \
  -v $(pwd):/workspace -w /workspace \
  node:18 npx task-master-ai models --setup
```

## 🔍 Debugging Examples

```bash
# Enable debug mode
TASKMASTER_DEBUG=true npx task-master-ai parse-prd requirements.md

# Verbose output
npx task-master-ai --verbose list-tasks

# Dry run mode (if supported)
npx task-master-ai parse-prd requirements.md --dry-run
```

## 📊 Output Format Examples

### JSON Output

```bash
npx task-master-ai list-tasks --format json
```

```json
{
  "tasks": [
    {
      "id": 1,
      "title": "Setup Authentication",
      "status": "pending",
      "priority": "high"
    }
  ]
}
```

### Table Output

```bash
npx task-master-ai list-tasks --format table
```

```
┌────┬─────────────────────┬─────────┬──────────┐
│ ID │ Title               │ Status  │ Priority │
├────┼─────────────────────┼─────────┼──────────┤
│ 1  │ Setup Authentication│ pending │ high     │
└────┴─────────────────────┴─────────┴──────────┘
```

## 🚨 Troubleshooting

```bash
# Check version
npx task-master-ai --version

# Verify installation
npx task-master-ai --help

# Clear NPX cache if needed
npx clear-npx-cache
npm cache clean --force

# Re-download latest version
npx task-master-ai@latest --version
```

## 📚 Help and Documentation

```bash
# General help
npx task-master-ai --help

# Command-specific help
npx task-master-ai parse-prd --help
npx task-master-ai add-task --help
npx task-master-ai models --help
```
