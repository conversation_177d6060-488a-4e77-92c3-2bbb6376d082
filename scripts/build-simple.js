#!/usr/bin/env node

/**
 * Simple bundling script using esbuild
 * Fixes shebang issues and creates clean single files
 */

import { build } from 'esbuild';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🔨 Simple bundling with esbuild...\n');

// Build configurations
const builds = [
    {
        name: 'CLI Tool',
        input: 'bin/task-master.js',
        output: 'dist/task-master-simple.js'
    },
    {
        name: 'MCP Server',
        input: 'mcp-server/server.js',
        output: 'dist/task-master-mcp-simple.js'
    }
];

// Clean and simple esbuild options
const buildOptions = {
    bundle: true,
    platform: 'node',
    target: 'node18',
    format: 'esm',
    minify: false,  // Keep readable for debugging
    sourcemap: false,
    external: [
        // Keep Node.js built-ins external
        'fs', 'path', 'os', 'crypto', 'util', 'events', 'stream', 'url',
        'querystring', 'http', 'https', 'net', 'tls', 'zlib', 'child_process',
        'cluster', 'worker_threads', 'readline', 'assert', 'buffer', 'console',
        'constants', 'dgram', 'dns', 'domain', 'module', 'perf_hooks',
        'process', 'punycode', 'string_decoder', 'timers', 'tty', 'v8', 'vm',
        // Problematic packages
        'fsevents', 'cpu-features'
    ],
    define: {
        'process.env.NODE_ENV': '"production"',
        'process.env.BUNDLED': '"true"'
    }
};

// Build function
async function buildFile(config) {
    console.log(`📦 Building ${config.name}...`);
    console.log(`   Input: ${config.input}`);
    console.log(`   Output: ${config.output}`);
    
    try {
        // Build without banner first
        await build({
            ...buildOptions,
            entryPoints: [config.input],
            outfile: config.output
        });
        
        // Read the generated file
        let content = fs.readFileSync(config.output, 'utf8');
        
        // Remove any existing shebangs
        content = content.replace(/^#!.*\n/gm, '');
        
        // Add single shebang at the beginning
        content = '#!/usr/bin/env node\n' + content;
        
        // Write back the corrected content
        fs.writeFileSync(config.output, content);
        
        // Make executable
        fs.chmodSync(config.output, '755');
        
        // Get file size
        const stats = fs.statSync(config.output);
        const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        
        console.log(`   ✅ Built successfully (${fileSizeInMB} MB)`);
        return true;
        
    } catch (error) {
        console.error(`   ❌ Build failed: ${error.message}`);
        return false;
    }
}

// Create dist directory
const distDir = path.join(projectRoot, 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
}

// Build all files
async function buildAll() {
    let successCount = 0;
    
    for (const config of builds) {
        const success = await buildFile(config);
        if (success) successCount++;
        console.log('');
    }
    
    console.log('📊 Simple Build Summary:');
    console.log(`✅ Successful: ${successCount}/${builds.length}`);
    console.log(`📁 Output directory: ${distDir}`);
    
    if (successCount === builds.length) {
        console.log('\n🎉 All simple builds completed successfully!');
        console.log('\n📋 Test the builds:');
        console.log('  ./dist/task-master-simple.js --version');
        console.log('  ./dist/task-master-mcp-simple.js --help');
        console.log('\n📦 These files are self-contained and include all dependencies.');
        
    } else {
        console.log('\n⚠️  Some builds failed. Check the output above.');
        process.exit(1);
    }
}

// Run the build
buildAll().catch(error => {
    console.error('❌ Build process failed:', error);
    process.exit(1);
});
