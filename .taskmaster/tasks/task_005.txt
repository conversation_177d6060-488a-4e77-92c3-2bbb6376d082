# Task ID: 5
# Title: Implement Command-Line Interface (CLI)
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Build a user-friendly CLI using click, supporting file/directory input, output options, and help messages.
# Details:
- Use click to define CLI commands and options
- Support input of single PDF or directory
- Allow output directory and format selection
- Provide --help and usage examples
- Integrate with core modules (PDF, OCR, Markdown)
- Display clear error messages and suggestions

# Test Strategy:
- Run CLI with various argument combinations
- Check help output and error handling
- Validate end-to-end processing via CLI
