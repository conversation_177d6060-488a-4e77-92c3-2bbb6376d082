#!/usr/bin/env node

/**
 * Rollup-based single file builder
 * Simpler alternative for bundling Node.js applications
 */

import { rollup } from 'rollup';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import { terser } from 'rollup-plugin-terser';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Build configurations
const builds = [
    {
        input: 'bin/task-master.js',
        output: 'dist/task-master-rollup.js',
        name: 'CLI Tool'
    },
    {
        input: 'mcp-server/server.js', 
        output: 'dist/task-master-mcp-rollup.js',
        name: 'MCP Server'
    }
];

// Common rollup configuration
const createConfig = (input, output) => ({
    input,
    output: {
        file: output,
        format: 'es',
        banner: '#!/usr/bin/env node'
    },
    external: [
        'fs',
        'path',
        'os',
        'crypto',
        'util',
        'events',
        'stream',
        'url',
        'querystring',
        'http',
        'https',
        'net',
        'tls',
        'zlib',
        'child_process',
        'cluster',
        'worker_threads',
        'fsevents'
    ],
    plugins: [
        nodeResolve({
            preferBuiltins: true,
            exportConditions: ['node']
        }),
        commonjs({
            ignoreDynamicRequires: true
        }),
        json(),
        terser({
            mangle: false,
            compress: {
                drop_console: false
            }
        })
    ]
});

// Build function
async function buildWithRollup() {
    console.log('🎯 Building with Rollup...\n');
    
    // Ensure dist directory exists
    const distDir = path.join(projectRoot, 'dist');
    if (!fs.existsSync(distDir)) {
        fs.mkdirSync(distDir, { recursive: true });
    }
    
    let successCount = 0;
    
    for (const build of builds) {
        console.log(`📦 Building ${build.name}...`);
        console.log(`   Input: ${build.input}`);
        console.log(`   Output: ${build.output}`);
        
        try {
            const bundle = await rollup(createConfig(build.input, build.output));
            await bundle.write(createConfig(build.input, build.output).output);
            await bundle.close();
            
            // Make executable
            fs.chmodSync(build.output, '755');
            
            // Get file size
            const stats = fs.statSync(build.output);
            const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            
            console.log(`   ✅ Built successfully (${fileSizeInMB} MB)`);
            successCount++;
            
        } catch (error) {
            console.error(`   ❌ Build failed: ${error.message}`);
        }
        
        console.log('');
    }
    
    console.log(`📊 Rollup build completed: ${successCount}/${builds.length} successful`);
    
    if (successCount === builds.length) {
        console.log('\n🎉 All Rollup builds completed successfully!');
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    buildWithRollup().catch(console.error);
}

export { buildWithRollup };
