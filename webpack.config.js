/**
 * Webpack configuration for single-file bundling
 * Alternative to esbuild for more complex bundling needs
 */

import path from 'path';
import { fileURLToPath } from 'url';
import webpack from 'webpack';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const config = {
    target: 'node',
    mode: 'production',
    
    entry: {
        'task-master': './bin/task-master.js',
        'task-master-mcp': './mcp-server/server.js'
    },
    
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: '[name]-webpack.js',
        clean: true
    },
    
    resolve: {
        extensions: ['.js', '.json'],
        preferRelative: true
    },
    
    externals: {
        // Keep these as external to avoid bundling issues
        'fsevents': 'commonjs fsevents',
        'cpu-features': 'commonjs cpu-features'
    },
    
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: [
                            ['@babel/preset-env', {
                                targets: { node: '18' }
                            }]
                        ]
                    }
                }
            },
            {
                test: /\.(txt|md)$/,
                type: 'asset/source'
            },
            {
                test: /\.json$/,
                type: 'json'
            }
        ]
    },
    
    plugins: [
        new webpack.BannerPlugin({
            banner: '#!/usr/bin/env node',
            raw: true
        }),
        
        new webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('production'),
            'process.env.BUNDLED': JSON.stringify('true')
        })
    ],
    
    optimization: {
        minimize: true,
        usedExports: true,
        sideEffects: false
    },
    
    stats: {
        colors: true,
        modules: false,
        chunks: false,
        chunkModules: false
    }
};

export default config;
