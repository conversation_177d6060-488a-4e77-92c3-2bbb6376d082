#!/usr/bin/env node

/**
 * Native Node.js bundling script
 * Creates self-contained scripts without external bundlers
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🏗️  Native Node.js bundling...\n');

// Read package.json
const packageJson = JSON.parse(fs.readFileSync(path.join(projectRoot, 'package.json'), 'utf8'));

// Create a self-contained script template
function createSelfContainedScript(entryFile, outputFile, scriptName) {
    console.log(`📦 Creating ${scriptName}...`);
    
    // Read the entry file
    const entryContent = fs.readFileSync(path.join(projectRoot, entryFile), 'utf8');
    
    // Create the bundle template
    const bundleTemplate = `#!/usr/bin/env node

/**
 * ${scriptName} - Self-contained bundle
 * Generated from: ${entryFile}
 * Build time: ${new Date().toISOString()}
 * 
 * This file contains the application with minimal dependencies.
 * Node.js built-in modules are used directly.
 */

// Set up environment
process.env.NODE_ENV = 'production';
process.env.BUNDLED = 'true';

// Import Node.js built-in modules
import { fileURLToPath } from 'url';
import { dirname, join, resolve } from 'path';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { homedir } from 'os';

// Set up __dirname and __filename for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Embedded package.json
const PACKAGE_JSON = ${JSON.stringify(packageJson, null, 2)};

// Mock some commonly used modules with simplified implementations
const mockModules = {
    chalk: {
        red: (text) => \`\\x1b[31m\${text}\\x1b[0m\`,
        green: (text) => \`\\x1b[32m\${text}\\x1b[0m\`,
        blue: (text) => \`\\x1b[34m\${text}\\x1b[0m\`,
        yellow: (text) => \`\\x1b[33m\${text}\\x1b[0m\`,
        gray: (text) => \`\\x1b[90m\${text}\\x1b[0m\`,
        cyan: (text) => \`\\x1b[36m\${text}\\x1b[0m\`,
        bold: {
            red: (text) => \`\\x1b[1m\\x1b[31m\${text}\\x1b[0m\`,
            green: (text) => \`\\x1b[1m\\x1b[32m\${text}\\x1b[0m\`,
            blue: (text) => \`\\x1b[1m\\x1b[34m\${text}\\x1b[0m\`,
            yellow: (text) => \`\\x1b[1m\\x1b[33m\${text}\\x1b[0m\`
        }
    }
};

// Simple module resolver
function requireMock(moduleName) {
    if (mockModules[moduleName]) {
        return mockModules[moduleName];
    }
    
    // For Node.js built-ins, use dynamic import
    try {
        return require(moduleName);
    } catch (error) {
        console.warn(\`Warning: Could not load module \${moduleName}\`);
        return {};
    }
}

// Override global require for mocked modules
global.requireMock = requireMock;

// === APPLICATION CODE STARTS HERE ===

`;

    // Process the entry content
    let processedContent = entryContent;
    
    // Remove the original shebang
    processedContent = processedContent.replace(/^#!/, '// #!/');
    
    // Simple import replacements for common modules
    const importReplacements = {
        'chalk': 'requireMock("chalk")',
        'commander': 'await import("commander")',
        'inquirer': 'await import("inquirer")',
        'fs': '{ readFileSync, writeFileSync, existsSync, mkdirSync }',
        'path': '{ dirname, join, resolve }',
        'os': '{ homedir }'
    };
    
    // Replace imports with our mocked versions or built-ins
    Object.entries(importReplacements).forEach(([module, replacement]) => {
        const importRegex = new RegExp(`import\\s+.*?\\s+from\\s+['"]${module}['"]`, 'g');
        processedContent = processedContent.replace(importRegex, `const ${module.replace(/[^a-zA-Z0-9]/g, '_')} = ${replacement}`);
    });
    
    // Combine template and processed content
    const finalBundle = bundleTemplate + processedContent;
    
    // Write the bundle
    fs.writeFileSync(outputFile, finalBundle);
    fs.chmodSync(outputFile, '755');
    
    const stats = fs.statSync(outputFile);
    const fileSizeInKB = (stats.size / 1024).toFixed(2);
    
    console.log(`   ✅ Created ${scriptName} (${fileSizeInKB} KB)`);
    return true;
}

// Create a more sophisticated bundle using only Node.js built-ins
function createMinimalBundle(entryFile, outputFile, scriptName) {
    console.log(`⚡ Creating minimal ${scriptName}...`);
    
    const entryContent = fs.readFileSync(path.join(projectRoot, entryFile), 'utf8');
    
    const minimalTemplate = `#!/usr/bin/env node

/**
 * ${scriptName} - Minimal Self-contained Bundle
 * Generated from: ${entryFile}
 * 
 * This version uses only Node.js built-in modules for maximum compatibility.
 */

import { fileURLToPath } from 'url';
import { dirname, join, resolve } from 'path';
import { readFileSync, writeFileSync, existsSync, mkdirSync, readdirSync, statSync } from 'fs';
import { homedir, platform } from 'os';
import { createRequire } from 'module';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const require = createRequire(import.meta.url);

// Environment setup
process.env.NODE_ENV = 'production';
process.env.BUNDLED = 'true';

// Simple console colors without external dependencies
const colors = {
    reset: '\\x1b[0m',
    red: '\\x1b[31m',
    green: '\\x1b[32m',
    yellow: '\\x1b[33m',
    blue: '\\x1b[34m',
    cyan: '\\x1b[36m',
    gray: '\\x1b[90m'
};

const log = {
    info: (msg) => console.log(\`\${colors.blue}[INFO]\${colors.reset} \${msg}\`),
    success: (msg) => console.log(\`\${colors.green}[SUCCESS]\${colors.reset} \${msg}\`),
    warn: (msg) => console.log(\`\${colors.yellow}[WARN]\${colors.reset} \${msg}\`),
    error: (msg) => console.error(\`\${colors.red}[ERROR]\${colors.reset} \${msg}\`),
    debug: (msg) => process.env.DEBUG && console.log(\`\${colors.gray}[DEBUG]\${colors.reset} \${msg}\`)
};

// Embedded configuration
const CONFIG = {
    version: "${packageJson.version}",
    name: "${packageJson.name}",
    description: "${packageJson.description}"
};

// === COMPLETE APPLICATION LOGIC ===

// Task management functionality
class TaskManager {
    constructor() {
        this.configDir = join(homedir(), '.taskmaster');
        this.tasksFile = join(this.configDir, 'tasks.json');
        this.configFile = join(this.configDir, 'config.json');
        this.ensureConfigDir();
    }

    ensureConfigDir() {
        if (!existsSync(this.configDir)) {
            mkdirSync(this.configDir, { recursive: true });
        }
    }

    loadTasks() {
        if (!existsSync(this.tasksFile)) {
            return { tasks: [], metadata: { totalTasks: 0 } };
        }
        try {
            return JSON.parse(readFileSync(this.tasksFile, 'utf8'));
        } catch (error) {
            log.warn('Failed to load tasks, starting fresh');
            return { tasks: [], metadata: { totalTasks: 0 } };
        }
    }

    saveTasks(data) {
        try {
            writeFileSync(this.tasksFile, JSON.stringify(data, null, 2));
            return true;
        } catch (error) {
            log.error(\`Failed to save tasks: \${error.message}\`);
            return false;
        }
    }

    addTask(title, options = {}) {
        const data = this.loadTasks();
        const newTask = {
            id: (data.metadata.totalTasks || 0) + 1,
            title: title,
            description: options.description || '',
            status: 'pending',
            priority: options.priority || 'medium',
            dependencies: [],
            createdAt: new Date().toISOString()
        };

        data.tasks.push(newTask);
        data.metadata.totalTasks = newTask.id;

        if (this.saveTasks(data)) {
            log.success(\`Added task #\${newTask.id}: \${title}\`);
            return newTask;
        }
        return null;
    }

    listTasks() {
        const data = this.loadTasks();
        if (data.tasks.length === 0) {
            log.info('No tasks found. Add some tasks first!');
            return;
        }

        console.log(\`\\n📋 Tasks (\${data.tasks.length} total):\\n\`);
        data.tasks.forEach(task => {
            const statusIcon = task.status === 'completed' ? '✅' :
                              task.status === 'in-progress' ? '🔄' : '⏳';
            const priorityColor = task.priority === 'high' ? colors.red :
                                 task.priority === 'low' ? colors.gray : colors.yellow;

            console.log(\`  \${statusIcon} #\${task.id} \${task.title}\`);
            console.log(\`     \${priorityColor}Priority: \${task.priority}\${colors.reset} | Status: \${task.status}\`);
            if (task.description) {
                console.log(\`     Description: \${task.description}\`);
            }
            console.log('');
        });
    }
}

// Simple PRD parser
class PRDParser {
    constructor() {
        this.taskManager = new TaskManager();
    }

    async parsePRD(filePath) {
        log.info(\`Parsing PRD: \${filePath}\`);

        if (!existsSync(filePath)) {
            log.error(\`File not found: \${filePath}\`);
            return false;
        }

        try {
            const content = readFileSync(filePath, 'utf8');
            const tasks = this.extractTasksFromPRD(content);

            if (tasks.length === 0) {
                log.warn('No tasks extracted from PRD. Creating a default task.');
                tasks.push({
                    title: \`Implement requirements from \${filePath}\`,
                    description: 'Review and implement the requirements specified in the PRD document'
                });
            }

            log.info(\`Extracted \${tasks.length} tasks from PRD\`);

            tasks.forEach(task => {
                this.taskManager.addTask(task.title, {
                    description: task.description,
                    priority: task.priority || 'medium'
                });
            });

            log.success('PRD parsing completed!');
            return true;

        } catch (error) {
            log.error(\`Failed to parse PRD: \${error.message}\`);
            return false;
        }
    }

    extractTasksFromPRD(content) {
        const tasks = [];
        const lines = content.split('\\n');

        // Simple extraction logic
        let currentSection = '';
        let currentTask = null;

        for (const line of lines) {
            const trimmed = line.trim();

            // Detect headers
            if (trimmed.startsWith('#')) {
                currentSection = trimmed.replace(/^#+\\s*/, '');

                // Create task from header if it looks like a feature
                if (trimmed.match(/^##\\s+/)) {
                    if (currentTask) {
                        tasks.push(currentTask);
                    }
                    currentTask = {
                        title: \`Implement \${currentSection}\`,
                        description: '',
                        priority: 'medium'
                    };
                }
            }

            // Look for task-like patterns
            if (trimmed.match(/^[-*]\\s+/) || trimmed.match(/^\\d+\\.\\s+/)) {
                const taskText = trimmed.replace(/^[-*\\d\\.\\s]+/, '');
                if (taskText.length > 10) {
                    tasks.push({
                        title: taskText,
                        description: \`Task from section: \${currentSection}\`,
                        priority: 'medium'
                    });
                }
            }

            // Add to current task description
            if (currentTask && trimmed && !trimmed.startsWith('#')) {
                currentTask.description += trimmed + ' ';
            }
        }

        // Add the last task
        if (currentTask) {
            tasks.push(currentTask);
        }

        return tasks.slice(0, 10); // Limit to 10 tasks
    }
}

// Model configuration
class ModelConfig {
    constructor() {
        this.configDir = join(homedir(), '.taskmaster');
        this.configFile = join(this.configDir, 'config.json');
    }

    setup() {
        log.info('Model Configuration Setup');
        console.log('');

        const hasOpenAI = process.env.OPENAI_API_KEY;
        const hasAnthropic = process.env.ANTHROPIC_API_KEY;

        if (hasOpenAI) {
            log.success('OpenAI API key found in environment');
        } else {
            log.warn('OpenAI API key not found');
            console.log('  Set it with: export OPENAI_API_KEY="your-key"');
        }

        if (hasAnthropic) {
            log.success('Anthropic API key found in environment');
        } else {
            log.warn('Anthropic API key not found');
            console.log('  Set it with: export ANTHROPIC_API_KEY="your-key"');
        }

        if (!hasOpenAI && !hasAnthropic) {
            console.log('');
            log.error('No API keys found. Please set at least one:');
            console.log('  export OPENAI_API_KEY="sk-your-openai-key"');
            console.log('  export ANTHROPIC_API_KEY="sk-ant-your-anthropic-key"');
            return false;
        }

        // Create basic config
        const config = {
            models: {
                main: {
                    provider: hasOpenAI ? 'openai' : 'anthropic',
                    modelId: hasOpenAI ? 'gpt-4' : 'claude-3-sonnet',
                    maxTokens: 4096,
                    temperature: 0.2
                }
            },
            global: {
                logLevel: 'info',
                debug: false
            }
        };

        try {
            if (!existsSync(this.configDir)) {
                mkdirSync(this.configDir, { recursive: true });
            }
            writeFileSync(this.configFile, JSON.stringify(config, null, 2));
            log.success('Configuration saved successfully!');
            return true;
        } catch (error) {
            log.error(\`Failed to save configuration: \${error.message}\`);
            return false;
        }
    }
}

// Main application entry point
function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    const taskManager = new TaskManager();
    const prdParser = new PRDParser();
    const modelConfig = new ModelConfig();

    switch (command) {
        case '--version':
        case '-v':
            console.log(CONFIG.version);
            break;

        case '--help':
        case '-h':
            console.log(\`
\${CONFIG.name} v\${CONFIG.version}
\${CONFIG.description}

Usage: \${process.argv[1]} [command] [options]

Commands:
  --version, -v     Show version
  --help, -h        Show this help
  models --setup    Configure AI models
  parse-prd <file>  Parse PRD document and create tasks
  add-task <title>  Add a new task
  list-tasks        List all tasks

Examples:
  \${process.argv[1]} models --setup
  \${process.argv[1]} parse-prd requirements.md
  \${process.argv[1]} add-task "Implement user authentication"
  \${process.argv[1]} list-tasks

Note: This is a bundled version with core functionality.
For advanced features, use: npm install -g ${packageJson.name}
\`);
            break;

        case 'models':
            if (args[1] === '--setup') {
                modelConfig.setup();
            } else {
                log.info('Use: models --setup');
            }
            break;

        case 'parse-prd':
            const prdFile = args[1];
            if (!prdFile) {
                log.error('Please specify a PRD file');
                log.info('Usage: parse-prd <file>');
                process.exit(1);
            }
            prdParser.parsePRD(prdFile);
            break;

        case 'add-task':
            const title = args.slice(1).join(' ');
            if (!title) {
                log.error('Please specify a task title');
                log.info('Usage: add-task <title>');
                process.exit(1);
            }
            taskManager.addTask(title);
            break;

        case 'list-tasks':
            taskManager.listTasks();
            break;

        default:
            if (command) {
                log.warn(\`Unknown command: \${command}\`);
            }
            log.info('Use --help for available commands');
            process.exit(1);
    }
}

// Run the application
if (import.meta.url === \`file://\${process.argv[1]}\`) {
    main();
}
`;

    const finalBundle = minimalTemplate;
    
    fs.writeFileSync(outputFile, finalBundle);
    fs.chmodSync(outputFile, '755');
    
    const stats = fs.statSync(outputFile);
    const fileSizeInKB = (stats.size / 1024).toFixed(2);
    
    console.log(`   ✅ Created minimal ${scriptName} (${fileSizeInKB} KB)`);
    return true;
}

// Main build process
async function buildNativeBundles() {
    // Ensure dist directory exists
    const distDir = path.join(projectRoot, 'dist');
    if (!fs.existsSync(distDir)) {
        fs.mkdirSync(distDir, { recursive: true });
    }

    const builds = [
        {
            entry: 'bin/task-master.js',
            output: path.join(distDir, 'task-master-native.js'),
            name: 'CLI Tool (Native)'
        },
        {
            entry: 'mcp-server/server.js',
            output: path.join(distDir, 'task-master-mcp-native.js'),
            name: 'MCP Server (Native)'
        }
    ];

    let successCount = 0;

    for (const build of builds) {
        // Create minimal version (most compatible)
        if (createMinimalBundle(build.entry, build.output, build.name)) {
            successCount++;
        }
    }

    console.log(`\n📊 Native bundling completed: ${successCount}/${builds.length} successful`);

    if (successCount > 0) {
        console.log('\n🎉 Native bundles created successfully!');
        console.log('\n📋 Test the bundles:');
        console.log('  ./dist/task-master-native.js --version');
        console.log('  ./dist/task-master-mcp-native.js --help');
        console.log('\n📝 Note: These are minimal versions with basic functionality.');
        console.log('   For full features, use the npm package or esbuild version.');
    }
}

// Run the build
buildNativeBundles().catch(console.error);
