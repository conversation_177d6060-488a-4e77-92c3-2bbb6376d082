/**
 * openai.js
 * AI provider implementation for OpenAI models using Vercel AI SDK.
 */

import { createOpenAI } from '@ai-sdk/openai';
import { generateText, generateObject } from 'ai';
import { BaseAIProvider } from './base-provider.js';
import { log } from '../../scripts/modules/index.js';

export class OpenAIProvider extends BaseAIProvider {
	constructor() {
		super();
		this.name = 'OpenAI';
	}

	/**
	 * Checks if the model ID indicates a Claude model being proxied through OpenAI gateway
	 * @param {string} modelId - The model identifier
	 * @returns {boolean} True if this is a Claude model
	 */
	isClaudeModel(modelId) {
		if (!modelId) return false;
		const lowerModelId = modelId.toLowerCase();
		const isClaudeModel = lowerModelId.includes('claude') ||
		                     lowerModelId.includes('anthropic') ||
		                     lowerModelId.startsWith('claude-');

		log('debug', `Model ID check: ${modelId} -> isClaudeModel: ${isClaudeModel}`);
		console.log(`[OpenAI Provider] Model ID check: ${modelId} -> isClaudeModel: ${isClaudeModel}`);
		return isClaudeModel;
	}

	/**
	 * Converts Anthropic API response format to OpenAI format
	 * @param {Object} anthropicResponse - Raw response from Anthropic API
	 * @returns {Object} OpenAI-compatible response format
	 */
	convertAnthropicToOpenAIFormat(anthropicResponse) {
		try {
			// Handle Anthropic response format: { content: [{ text: "..." }], usage: {...} }
			if (anthropicResponse.content && Array.isArray(anthropicResponse.content)) {
				const textContent = anthropicResponse.content
					.filter(item => item.type === 'text')
					.map(item => item.text)
					.join('');

				return {
					choices: [{
						message: {
							content: textContent,
							role: 'assistant'
						},
						finish_reason: 'stop',
						index: 0
					}],
					usage: {
						prompt_tokens: anthropicResponse.usage?.input_tokens || 0,
						completion_tokens: anthropicResponse.usage?.output_tokens || 0,
						total_tokens: (anthropicResponse.usage?.input_tokens || 0) + (anthropicResponse.usage?.output_tokens || 0)
					}
				};
			}

			// Handle tool use responses for generateObject
			if (anthropicResponse.content && anthropicResponse.content.some(item => item.type === 'tool_use')) {
				const toolUse = anthropicResponse.content.find(item => item.type === 'tool_use');

				return {
					choices: [{
						message: {
							tool_calls: [{
								id: toolUse.id,
								type: 'function',
								function: {
									name: toolUse.name,
									arguments: JSON.stringify(toolUse.input)
								}
							}],
							role: 'assistant'
						},
						finish_reason: 'tool_calls',
						index: 0
					}],
					usage: {
						prompt_tokens: anthropicResponse.usage?.input_tokens || 0,
						completion_tokens: anthropicResponse.usage?.output_tokens || 0,
						total_tokens: (anthropicResponse.usage?.input_tokens || 0) + (anthropicResponse.usage?.output_tokens || 0)
					}
				};
			}

			// If it's already in OpenAI format, return as-is
			if (anthropicResponse.choices) {
				return anthropicResponse;
			}

			// Fallback: wrap unknown format
			log('warn', 'Unknown Anthropic response format, attempting basic conversion');
			return {
				choices: [{
					message: {
						content: JSON.stringify(anthropicResponse),
						role: 'assistant'
					},
					finish_reason: 'stop',
					index: 0
				}],
				usage: {
					prompt_tokens: 0,
					completion_tokens: 0,
					total_tokens: 0
				}
			};

		} catch (error) {
			log('error', `Error converting Anthropic response to OpenAI format: ${error.message}`);
			throw new Error(`Response format conversion failed: ${error.message}`);
		}
	}

	/**
	 * Override generateText to handle Claude model responses
	 */
	async generateText(params) {
		try {
			this.validateParams(params);
			this.validateMessages(params.messages);

			log('debug', `Generating ${this.name} text with model: ${params.modelId}`);

			const client = this.getClient(params);

			// Check if this is a Claude model being proxied
			if (this.isClaudeModel(params.modelId)) {
				log('debug', 'Detected Claude model, will handle response format conversion');

				try {
					// Make the API call using Vercel AI SDK
					const result = await generateText({
						model: client(params.modelId),
						messages: params.messages,
						maxTokens: params.maxTokens,
						temperature: params.temperature
					});

					// For Claude models, the result should already be processed by Vercel AI SDK
					// But if the gateway returns raw Anthropic format, we need to handle it
					return {
						text: result.text,
						usage: {
							inputTokens: result.usage?.promptTokens,
							outputTokens: result.usage?.completionTokens,
							totalTokens: result.usage?.totalTokens
						}
					};

				} catch (error) {
					log('debug', `Error in generateText for Claude model: ${error.message}`);
					log('debug', `Error details: ${JSON.stringify(error, null, 2)}`);

					// Check if this is a format conversion error or gateway error
					if (error.message && (error.message.includes('choices') || error.message.includes('请求参数model错误'))) {
						log('warn', 'Detected response format issue or model name error, attempting to handle');

						// Try to extract the raw response from the error
						let rawResponse = null;

						// Check different possible locations for the response
						if (error.responseBody) {
							try {
								rawResponse = JSON.parse(error.responseBody);
								log('debug', `Parsed responseBody: ${JSON.stringify(rawResponse, null, 2)}`);
							} catch (parseError) {
								log('error', 'Failed to parse error responseBody');
							}
						}

						// Check if error contains the actual response data
						if (error.cause?.value && typeof error.cause.value === 'object') {
							rawResponse = error.cause.value;
							log('debug', `Found response in error.cause.value: ${JSON.stringify(rawResponse, null, 2)}`);
						}

						// If we have a raw response, try to convert it
						if (rawResponse) {
							// Check if it's an error response
							if (rawResponse.error) {
								log('error', `Gateway error: ${rawResponse.error.message || rawResponse.error.code}`);
								throw new Error(`Gateway error: ${rawResponse.error.message || rawResponse.error.code}`);
							}

							// Try to convert Anthropic format to OpenAI format
							try {
								const convertedResponse = this.convertAnthropicToOpenAIFormat(rawResponse);

								// Extract text from converted response
								const text = convertedResponse.choices[0]?.message?.content || '';

								return {
									text: text,
									usage: {
										inputTokens: convertedResponse.usage?.prompt_tokens || 0,
										outputTokens: convertedResponse.usage?.completion_tokens || 0,
										totalTokens: convertedResponse.usage?.total_tokens || 0
									}
								};
							} catch (conversionError) {
								log('error', `Failed to convert response format: ${conversionError.message}`);
							}
						}
					}

					throw error;
				}
			} else {
				// For regular OpenAI models, use the parent implementation
				return await super.generateText(params);
			}

		} catch (error) {
			this.handleError('text generation', error);
		}
	}

	/**
	 * Override generateObject to handle Claude model responses
	 */
	async generateObject(params) {
		try {
			this.validateParams(params);
			this.validateMessages(params.messages);

			if (!params.schema) {
				throw new Error('Schema is required for object generation');
			}
			if (!params.objectName) {
				throw new Error('Object name is required for object generation');
			}

			log('f', `Generating ${this.name} object ('${params.objectName}') with model: ${params.modelId}`);

			const client = this.getClient(params);

			// Check if this is a Claude model being proxied
			if (this.isClaudeModel(params.modelId)) {
				log('debug', 'Detected Claude model for object generation, will handle response format conversion');

				try {
					// Make the API call using Vercel AI SDK
					const result = await generateObject({
						model: client(params.modelId),
						messages: params.messages,
						schema: params.schema,
						mode: 'tool',
						maxTokens: params.maxTokens,
						temperature: params.temperature
					});

					// For Claude models, the result should already be processed by Vercel AI SDK
					return {
						object: result.object,
						usage: {
							inputTokens: result.usage?.promptTokens,
							outputTokens: result.usage?.completionTokens,
							totalTokens: result.usage?.totalTokens
						}
					};

				} catch (error) {
					log('debug', `Error in generateObject for Claude model: ${error.message}`);
					log('debug', `Error details: ${JSON.stringify(error, null, 2)}`);

					// Check if this is a format conversion error or gateway error
					if (error.message && (error.message.includes('choices') || error.message.includes('请求参数model错误'))) {
						log('warn', 'Detected response format issue or model name error for object generation, attempting to handle');

						// Try to extract the raw response from the error
						let rawResponse = null;

						// Check different possible locations for the response
						if (error.responseBody) {
							try {
								rawResponse = JSON.parse(error.responseBody);
								log('debug', `Parsed responseBody for object: ${JSON.stringify(rawResponse, null, 2)}`);
							} catch (parseError) {
								log('error', 'Failed to parse error responseBody for object generation');
							}
						}

						// Check if error contains the actual response data
						if (error.cause?.value && typeof error.cause.value === 'object') {
							rawResponse = error.cause.value;
							log('debug', `Found response in error.cause.value for object: ${JSON.stringify(rawResponse, null, 2)}`);
						}

						// If we have a raw response, try to convert it
						if (rawResponse) {
							// Check if it's an error response
							if (rawResponse.error) {
								log('error', `Gateway error for object generation: ${rawResponse.error.message || rawResponse.error.code}`);
								throw new Error(`Gateway error: ${rawResponse.error.message || rawResponse.error.code}`);
							}

							// Try to convert Anthropic format to OpenAI format
							try {
								const convertedResponse = this.convertAnthropicToOpenAIFormat(rawResponse);

								// Extract object from converted response
								let objectData = null;

								// Check for tool calls (function calling format)
								if (convertedResponse.choices[0]?.message?.tool_calls) {
									const toolCall = convertedResponse.choices[0].message.tool_calls[0];
									if (toolCall?.function?.arguments) {
										try {
											objectData = JSON.parse(toolCall.function.arguments);
										} catch (parseError) {
											log('error', 'Failed to parse tool call arguments as JSON');
											throw new Error('Invalid JSON in tool call arguments');
										}
									}
								} else {
									// Fallback: try to parse content as JSON
									const content = convertedResponse.choices[0]?.message?.content || '';
									try {
										objectData = JSON.parse(content);
									} catch (parseError) {
										log('error', 'Failed to parse message content as JSON object');
										throw new Error('Response content is not valid JSON');
									}
								}

								return {
									object: objectData,
									usage: {
										inputTokens: convertedResponse.usage?.prompt_tokens || 0,
										outputTokens: convertedResponse.usage?.completion_tokens || 0,
										totalTokens: convertedResponse.usage?.total_tokens || 0
									}
								};
							} catch (conversionError) {
								log('error', `Failed to convert response format for object generation: ${conversionError.message}`);
							}
						}
					}

					throw error;
				}
			} else {
				// For regular OpenAI models, use the parent implementation
				return await super.generateObject(params);
			}

		} catch (error) {
			this.handleError('object generation', error);
		}
	}

	/**
	 * Creates and returns an OpenAI client instance.
	 * @param {object} params - Parameters for client initialization
	 * @param {string} params.apiKey - OpenAI API key
	 * @param {string} [params.baseURL] - Optional custom API endpoint
	 * @returns {Function} OpenAI client function
	 * @throws {Error} If API key is missing or initialization fails
	 */
	getClient(params) {
		try {
			const { apiKey, baseURL } = params;

			if (!apiKey) {
				throw new Error('OpenAI API key is required.');
			}

			return createOpenAI({
				apiKey,
				...(baseURL && { baseURL })
			});
		} catch (error) {
			this.handleError('client initialization', error);
		}
	}
}
