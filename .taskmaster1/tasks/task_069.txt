# Task ID: 69
# Title: Enhance Analyze Complexity for Specific Task IDs
# Status: done
# Dependencies: None
# Priority: medium
# Description: Modify the analyze-complexity feature (CLI and MCP) to allow analyzing only specified task IDs or ranges, and append/update results in the report.
# Details:

Implementation Plan:

1. **Core Logic (`scripts/modules/task-manager/analyze-task-complexity.js`)**
    * Modify function signature to accept optional parameters: `options.ids` (string, comma-separated IDs) and range parameters `options.from` and `options.to`.
    * If `options.ids` is present:
        * Parse the `ids` string into an array of target IDs.
        * Filter `tasksData.tasks` to include only tasks matching the target IDs.
        * Handle cases where provided IDs don't exist in `tasks.json`.
    * If range parameters (`options.from` and `options.to`) are present:
        * Parse these values into integers.
        * Filter tasks within the specified ID range (inclusive).
    * If neither `options.ids` nor range parameters are present: Continue with existing logic (filtering by active status).
    * Maintain existing logic for skipping completed tasks.
    * **Report Handling:**
        * Before generating analysis, check if the `outputPath` report file exists.
        * If it exists:
            * Read the existing `complexityAnalysis` array.
            * Generate new analysis only for target tasks (filtered by ID or range).
            * Merge results: Remove entries from the existing array that match IDs analyzed in the current run, then append new analysis results to the array.
            * Update the `meta` section (`generatedAt`, `tasksAnalyzed`).
            * Write merged `complexityAnalysis` and updated `meta` back to report file.
        * If the report file doesn't exist: Create it as usual.
    * **Prompt Generation:** Ensure `generateInternalComplexityAnalysisPrompt` receives correctly filtered list of tasks.

2. **CLI (`scripts/modules/commands.js`)**
    * Add new options to the `analyze-complexity` command:
        * `--id/-i <ids>`: "Comma-separated list of specific task IDs to analyze"
        * `--from/-f <startId>`: "Start ID for range analysis (inclusive)"
        * `--to/-t <endId>`: "End ID for range analysis (inclusive)"
    * In the `.action` handler:
        * Check if `options.id`, `options.from`, or `options.to` are provided.
        * If yes, pass appropriate values to the `analyzeTaskComplexity` core function via the `options` object.
        * Update user feedback messages to indicate specific task analysis.

3. **MCP Tool (`mcp-server/src/tools/analyze.js`)**
    * Add new optional parameters to Zod schema for `analyze_project_complexity` tool:
        * `ids: z.string().optional().describe("Comma-separated list of task IDs to analyze specifically")`
        * `from: z.number().optional().describe("Start ID for range analysis (inclusive)")`
        * `to: z.number().optional().describe("End ID for range analysis (inclusive)")`
    * In the `execute` method, pass `args.ids`, `args.from`, and `args.to` to the `analyzeTaskComplexityDirect` function within its `args` object.

4. **Direct Function (`mcp-server/src/core/direct-functions/analyze-task-complexity.js`)**
    * Update function to receive `ids`, `from`, and `to` values within the `args` object.
    * Pass these values along to the core `analyzeTaskComplexity` function within its `options` object.

5. **Documentation:** Update relevant rule files (`commands.mdc`, `taskmaster.mdc`) to reflect new `--id/-i`, `--from/-f`, and `--to/-t` options/parameters.

# Test Strategy:

1. **CLI:**
    * Run `task-master analyze-complexity -i=<id1>` (where report doesn't exist). Verify report created with only task id1.
    * Run `task-master analyze-complexity -i=<id2>` (where report exists). Verify report updated, containing analysis for both id1 and id2 (id2 replaces any previous id2 analysis).
    * Run `task-master analyze-complexity -i=<id1>,<id3>`. Verify report updated, containing id1, id2, id3.
    * Run `task-master analyze-complexity -f=50 -t=60`. Verify report created/updated with tasks in the range 50-60.
    * Run `task-master analyze-complexity` (no flags). Verify it analyzes all active tasks and updates the report accordingly, merging with previous specific analyses.
    * Test with invalid/non-existent IDs or ranges.
    * Verify that completed tasks are still skipped in all scenarios, maintaining existing behavior.
2. **MCP:**
    * Call `analyze_project_complexity` tool with `ids: "<id1>"`. Verify report creation/update.
    * Call `analyze_project_complexity` tool with `ids: "<id1>,<id2>,<id3>"`. Verify report created/updated with multiple specific tasks.
    * Call `analyze_project_complexity` tool with `from: 50, to: 60`. Verify report created/updated for tasks in range.
    * Call `analyze_project_complexity` tool without parameters. Verify full analysis and merging.
3. Verify report `meta` section is updated correctly on each run.

# Subtasks:
## 1. Modify core complexity analysis logic [done]
### Dependencies: None
### Description: Update the core complexity analysis function to accept specific task IDs or ranges as input parameters
### Details:
Refactor the existing complexity analysis module to allow filtering by task IDs or ranges. This involves modifying the data processing pipeline to filter tasks before analysis, ensuring the complexity metrics are calculated only for the specified tasks while maintaining context awareness.

## 2. Update CLI interface for task-specific complexity analysis [done]
### Dependencies: 69.1
### Description: Extend the CLI to accept task IDs or ranges as parameters for the complexity analysis command
### Details:
Add new flags `--id/-i`, `--from/-f`, and `--to/-t` to the CLI that allow users to specify task IDs or ranges for targeted complexity analysis. Update the command parser, help documentation, and ensure proper validation of the provided values.

## 3. Integrate task-specific analysis with MCP tool [done]
### Dependencies: 69.1
### Description: Update the MCP tool interface to support analyzing complexity for specific tasks or ranges
### Details:
Modify the MCP tool's API endpoints and UI components to allow users to select specific tasks or ranges for complexity analysis. Ensure the UI provides clear feedback about which tasks are being analyzed and update the visualization components to properly display partial analysis results.

## 4. Create comprehensive tests for task-specific complexity analysis [done]
### Dependencies: 69.1, 69.2, 69.3
### Description: Develop test cases to verify the correct functioning of task-specific complexity analysis
### Details:
Create unit and integration tests that verify the task-specific complexity analysis works correctly across both CLI and MCP interfaces. Include tests for edge cases such as invalid task IDs, tasks with dependencies outside the selected set, and performance tests for large task sets.

