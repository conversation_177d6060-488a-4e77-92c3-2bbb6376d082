{"name": "task-master-ai", "version": "0.16.2", "description": "A task management system for ambitious AI-driven development that doesn't overwhelm and confuse Cursor.", "main": "index.js", "type": "module", "bin": {"task-master": "bin/task-master.js", "task-master-mcp": "mcp-server/server.js", "task-master-ai": "mcp-server/server.js"}, "scripts": {"test": "node --experimental-vm-modules node_modules/.bin/jest", "test:fails": "node --experimental-vm-modules node_modules/.bin/jest --onlyFailures", "test:watch": "node --experimental-vm-modules node_modules/.bin/jest --watch", "test:coverage": "node --experimental-vm-modules node_modules/.bin/jest --coverage", "test:e2e": "./tests/e2e/run_e2e.sh", "test:e2e-report": "./tests/e2e/run_e2e.sh --analyze-log", "prepare": "chmod +x bin/task-master.js mcp-server/server.js", "changeset": "changeset", "release": "changeset publish", "inspector": "npx @modelcontextprotocol/inspector node mcp-server/server.js", "mcp-server": "node mcp-server/server.js", "format-check": "biome format .", "format": "biome format . --write"}, "keywords": ["claude", "task", "management", "ai", "development", "cursor", "anthropic", "llm", "mcp", "context"], "author": "<PERSON><PERSON>", "license": "MIT WITH Commons-<PERSON><PERSON>", "dependencies": {"@ai-sdk/amazon-bedrock": "^2.2.9", "@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/azure": "^1.3.17", "@ai-sdk/google": "^1.2.13", "@ai-sdk/google-vertex": "^2.2.23", "@ai-sdk/mistral": "^1.2.7", "@ai-sdk/openai": "^1.3.20", "@ai-sdk/perplexity": "^1.1.7", "@ai-sdk/xai": "^1.2.15", "@anthropic-ai/sdk": "^0.39.0", "@aws-sdk/credential-providers": "^3.817.0", "@openrouter/ai-sdk-provider": "^0.4.5", "ai": "^4.3.10", "boxen": "^8.0.1", "chalk": "^5.4.1", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^2.2.2", "figlet": "^1.8.0", "fuse.js": "^7.1.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "ollama-ai-provider": "^1.2.0", "openai": "^4.89.0", "ora": "^8.2.0", "uuid": "^11.1.0", "zod": "^3.23.8"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/eyaltoledano/claude-task-master.git"}, "homepage": "https://github.com/eyaltoledano/claude-task-master#readme", "bugs": {"url": "https://github.com/eyaltoledano/claude-task-master/issues"}, "files": ["scripts/**", "assets/**", ".cursor/**", "README-task-master.md", "index.js", "bin/**", "mcp-server/**", "src/**"], "overrides": {"node-fetch": "^2.6.12", "whatwg-url": "^11.0.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.28.1", "@types/jest": "^29.5.14", "execa": "^8.0.1", "ink": "^5.0.1", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "mock-fs": "^5.5.0", "prettier": "^3.5.3", "react": "^18.3.1", "supertest": "^7.1.0", "tsx": "^4.16.2"}}