# Task ID: 8
# Title: Add Logging and Error Handling Mechanisms
# Status: pending
# Dependencies: 7
# Priority: medium
# Description: Implement detailed logging for all modules and robust error handling with user-friendly messages.
# Details:
- Use Python logging module for info, warning, error logs
- Log key events: start/end, errors, retries, output paths
- Provide actionable error messages and troubleshooting tips
- Optionally support log file output

# Test Strategy:
- Induce errors (bad PDF, API failure) and check logs
- Validate log file creation and content
- Review user-facing error messages
